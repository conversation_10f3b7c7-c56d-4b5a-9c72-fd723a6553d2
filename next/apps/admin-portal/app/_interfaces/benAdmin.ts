import {
  AddOnProductResponse as BenAdminAddOnProductResponse,
  ChangeRequest as BenAdminChangeRequests,
  ContactDetails as BenAdminContactDetails,
  Organization as BenAdminOrganization,
  OrganizationDetails as BenAdminOrganizationDetails,
  PlanAncillary as BenAdminPlanAncillary,
  PlanContainer as BenAdminPlan,
  PlanDesign as BenAdminPlanDesign,
  PlanDocument as BenAdminPlanDocuments,
} from 'apps/admin-portal/components/benAdmin/Models/interfaces';

/***********************************************
 * 1) Reusable Paginated Response
 ***********************************************/
export interface PaginatedResponse<T> {
  data: T[];
  limit: number;
  offset: number;
  totalCount: number;
}

/***********************************************
 * 2) API Data Mapping
 ***********************************************/
export interface ApiDataMap {
  document: BenAdminPlanDocuments[];
  plan: BenAdminPlan;
  contacts: BenAdminContactDetails;
  changerequest: BenAdminChangeRequests | BenAdminChangeRequests[];
  organization: BenAdminOrganization;
  organizationDetails: BenAdminOrganizationDetails;
  fileDownload: any;
  publish: undefined;
  picklist: any;
  addOnProduct: BenAdminAddOnProductResponse;
  planDesignAncillary: BenAdminPlanAncillary;
  planDesign: BenAdminPlanDesign[];
}
