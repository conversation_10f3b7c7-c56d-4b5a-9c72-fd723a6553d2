'use client';
import { HStack, Icon, Link, Text, VStack } from '@chakra-ui/react';
import { Organization } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { useBenAdminV2 } from 'apps/admin-portal/components/benAdmin/organization/hooks/useBenAdminV2Hook';
import AutoTable from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Table';
import { QuickSearchOption } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Table/searchBar';
import { AutoTableColumns } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import { FaUser } from 'react-icons/fa';

import { useBenAdmin } from '../_hooks/useBenAdmin';

const BenAdmin = () => {
  const { useApiQuery } = useBenAdmin();
  const { flag } = useBenAdminV2();

  console.log('should us ben admin v2 ' + flag);
  // 1) Fetch organization details (skipped if in draft mode)
  const { organization: organizations } = useApiQuery([
    {
      key: 'organization',
      queryParams: { sort_by: 'name', order: 'asc' },
    },
  ]);

  const status = 'Assigned to Me';

  const columns: AutoTableColumns<Organization> = [
    {
      key: 'name',
      override: {
        headerName: 'Organization',
        data: (info) => {
          return {
            display: (
              <Link href={`/bop/organization/${info.organization_id}`}>
                {info.name}
              </Link>
            ),
            value: info.name,
          };
        },
      },
    },
    {
      key: 'status',
      override: {
        headerName: 'Status',
        data: (info) => {
          const evenId = info.organization_id % 2 === 0;
          const statusValue = evenId ? 'Assigned to Me' : 'Not Assigned To Me';

          return {
            display: (
              <HStack
                spacing={1.5}
                bg={evenId ? '#c6f6d5' : '#f6c6cc'}
                px={2.5}
                py={1}
                borderRadius="sm"
                height={'22px'}
                width="fit-content"
                fontWeight={'bold'}
                fontSize="12.5px"
                color={evenId ? '#396b53' : '#6b3953'}
              >
                <Icon as={FaUser} fontSize="11px" />
                <Text>{statusValue}</Text>
              </HStack>
            ),
            value: statusValue,
          };
        },
      },
    },
    { key: 'effective_date' },
    { key: 'pbm', override: { headerName: 'PBM' } },
    { key: 'brokerage_firm', override: { data: 'Cell Contents' } },
  ];

  const quickFilters: QuickSearchOption<Organization>[] = [
    {
      label: status,
      filterFunction: (data) => data.filter((item) => item.status === status),
    },
    {
      label: 'New Clients',
      filterFunction: (data) => {
        const now = new Date();
        const twoMonthsAgo = new Date();
        twoMonthsAgo.setMonth(now.getMonth() - 1);

        return data.filter((item) => {
          const createdDate = item.created_date
            ? new Date(item.created_date)
            : null;

          return (
            createdDate && createdDate >= twoMonthsAgo && createdDate <= now
          );
        });
      },
    },
    {
      label: 'Active Clients',
      filterFunction: (data) =>
        data.filter((item) => {
          const effectiveDate = item.effective_date
            ? new Date(item.effective_date)
            : null;
          const expirationDate = item.expiration_date
            ? new Date(item.expiration_date)
            : null;

          return (
            effectiveDate &&
            effectiveDate <= new Date() &&
            (!expirationDate || expirationDate >= new Date())
          );
        }),
    },
    {
      label: 'Last Visited',
      filterFunction: (data) =>
        data.filter((item) => item.status === 'Last Visited'),
    },
    {
      label: 'Termed',
      filterFunction: (data) =>
        data.filter((item) => {
          const expirationDate = item.expiration_date
            ? new Date(item.expiration_date)
            : null;

          return expirationDate && expirationDate < new Date();
        }),
    },
    {
      label: 'All',
      filterFunction: (data) => data,
      active: true,
    },
  ];

  return (
    <VStack
      backgroundColor="white"
      spacing={6}
      p={6}
      align="stretch"
      boxShadow="md"
      borderRadius="sm"
    >
      <AutoTable
        data={organizations as any}
        columns={columns}
        searchConfig={{
          placeholder: 'Search by Organization Name',
          searchField: 'name',
          hint: 'Search by the organization name',
          quickSearchOptions: quickFilters,
        }}
      />
    </VStack>
  );
};

export default BenAdmin;
