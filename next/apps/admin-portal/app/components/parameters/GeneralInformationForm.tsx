// File: src/components/parameters/ParametersForm.tsx

import { Button, useToast, VStack } from '@chakra-ui/react';
import React, { useEffect, useState } from 'react';

// import { GeneralInformation } from '@/types/parameters';
const defaultPerClaimRebates: any = {
  nonSpecialtyBrand30DS: '',
  nonSpecialtyBrand90DS: '',
  nonSpecialtyMailBrand: '',
  specialtyBrand: '',
};

const defaultLumpSumRebates: any = {
  amount: '',
  nonSpecialtyBrandPercentage: '',
  specialtyBrandPercentage: '',
};
export const getDefaultGeneralInformation = (): any => ({
  formulary: '4th PBM Closed Formulary',
  planExclusions: {
    lcv_wow: true,
    medical_benefit_only: true,
    desi: true,
    otc_drug_ind: false,
    abortifacient: false,
    weight_loss_inj: true,
    weight_loss_oral: true,
    fertility: false,
    growth_hormone: false,
    questionable_clinical_effectiveness: true,
  },
  dawPenalties: {
    daw1: true,
    daw2: true,
  },
  rebates: {
    incumbent: {
      type: 'useFromClaims',
      perClaimRebates: { ...defaultPerClaimRebates },
      lumpSumRebates: { ...defaultLumpSumRebates },
    },
    fourthPbm: {
      type: 'useFromClaims',
      perClaimRebates: { ...defaultPerClaimRebates },
      lumpSumRebates: { ...defaultLumpSumRebates },
    },
  },
  dispensingFee: '',
  flags: {
    mcap: true,
    pap: true,
    ids: true,
    hans: true,
  },
  adminFees: {
    perClaim: '2.80',
    illuminateRx: '10',
  },
  cotRate: '',
  copayModeling: {
    modelingType: 'memberCoinsurance',
    memberCopays: {
      nsRetailGeneric30: '',
      nsRetailPreferredBrand30: '',
      nsRetailNonPreferredBrand30: '',
      nsRetailGeneric90: '',
      nsRetailPreferredBrand90: '',
      nsRetailNonPreferredBrand90: '',
      nsMailGeneric90: '',
      nsMailPreferredBrand90: '',
      nsMailNonPreferredBrand90: '',
      specialtyGeneric: '',
      specialtyPreferredBrand: '',
      specialtyNonPreferredBrand: '',
    },
    memberCoinsurance: {
      nsRetailGeneric30: { percentage: '30', maximum: '150' },
      nsRetailPreferredBrand30: { percentage: '30', maximum: '150' },
      nsRetailNonPreferredBrand30: { percentage: '30', maximum: '150' },
      nsRetailGeneric90: { percentage: '30', maximum: '450' },
      nsRetailPreferredBrand90: { percentage: '30', maximum: '450' },
      nsRetailNonPreferredBrand90: { percentage: '30', maximum: '450' },
      nsMailGeneric90: { percentage: '30', maximum: '450' },
      nsMailPreferredBrand90: { percentage: '30', maximum: '450' },
      nsMailNonPreferredBrand90: { percentage: '30', maximum: '450' },
      specialtyGeneric: { percentage: '30', maximum: '1000' },
      specialtyPreferredBrand: { percentage: '30', maximum: '1000' },
      specialtyNonPreferredBrand: { percentage: '30', maximum: '1000' },
    },
    contractClinicalShift: {
      discounts: {
        spBrand: 17,
        mailBrand: 15,
        spGeneric: '16',
        mailGeneric: '14',
        retail30Brand: '11',
        retail90Brand: '13',
        retail30Generic: '10',
        retail90Generic: '12',
      },
      clinicalModelingType: 'fromFile',
    },
  },
});
export const isValidNumber = (value?: string): boolean => {
  if (!value) return false;

  // Allow any number format with any decimal precision
  const num = parseFloat(value);
  return !isNaN(num) && num >= 0;
};
export const validateRebateValues = (rebate: any): boolean => {
  if (rebate.type === 'lumpSum' && rebate.lumpSumRebates) {
    return (
      isValidNumber(rebate.lumpSumRebates.amount) ||
      !rebate.lumpSumRebates.amount
    );
  }

  if (rebate.type === 'perClaim' && rebate.perClaimRebates) {
    // Allow empty values or valid numbers
    return (
      (isValidNumber(rebate.perClaimRebates.nonSpecialtyBrand30DS) ||
        !rebate.perClaimRebates.nonSpecialtyBrand30DS) &&
      (isValidNumber(rebate.perClaimRebates.nonSpecialtyBrand90DS) ||
        !rebate.perClaimRebates.nonSpecialtyBrand90DS) &&
      (isValidNumber(rebate.perClaimRebates.nonSpecialtyMailBrand) ||
        !rebate.perClaimRebates.nonSpecialtyMailBrand) &&
      (isValidNumber(rebate.perClaimRebates.specialtyBrand) ||
        !rebate.perClaimRebates.specialtyBrand)
    );
  }

  return true;
};
export const validateGeneralInformation = (parameters: any): boolean => {
  // Validate required fields based on the form's requirements

  // Validate rebates based on selected type
  if (parameters.rebates) {
    // Validate incumbent
    if (parameters.rebates.incumbent) {
      if (!validateRebateValues(parameters.rebates.incumbent)) {
        return false;
      }
    }

    // Validate fourthPbm
    if (parameters.rebates.fourthPbm) {
      if (!validateRebateValues(parameters.rebates.fourthPbm)) {
        return false;
      }
    }
  }

  // Add more validation logic as needed for other sections

  return true;
};
// import {
//   getDefaultGeneralInformation,
//   validateGeneralInformation,
// } from '@/utils/parameterUtils';

import { AdminFeesSection } from './sections/AdminFeesSection';
import { CopayModelingSection } from './sections/CopayModelingSection';
import { DawPenaltiesSection } from './sections/DawPenaltiesSection';
import { FlagsSection } from './sections/FlagsSection';
import { FormularySection } from './sections/FormularySection';
import { PlanExclusionsSection } from './sections/PlanExclusionsSection';
import { RebatesSection } from './sections/RebatesSection';
import { CCSModeling } from './sections/RebatesSection/CCSModeling';
interface GeneralInformationFormProps {
  opportunityId: string;
  onSave: (generalInfo: any) => Promise<void>;
  initialGeneralInfo?: any;
}

export default function GeneralInformationForm({
  opportunityId,
  onSave,
  initialGeneralInfo,
}: GeneralInformationFormProps) {
  const [generalInfo, setGeneralInfo] = useState<any>(
    getDefaultGeneralInformation()
  );
  const [isLoading, setIsLoading] = useState(false);
  const toast = useToast();

  useEffect(() => {
    if (initialGeneralInfo) {
      setGeneralInfo(initialGeneralInfo);
    }
  }, [initialGeneralInfo]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateGeneralInformation(generalInfo)) {
      toast({
        title: 'Validation Error',
        description: 'Please check all required fields',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
      return;
    }

    setIsLoading(true);
    try {
      await onSave(generalInfo);
      toast({
        title: 'Success',
        description: 'General Information saved successfully',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (error) {
      toast({
        title: 'Error saving General Information',
        description:
          error instanceof Error ? error.message : 'An error occurred',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <VStack spacing={6} align="stretch">
        <FormularySection
          value={generalInfo.formulary}
          onChange={(value: any) =>
            setGeneralInfo((prev: any) => ({
              ...prev,
              formulary: value,
            }))
          }
        />
        <CCSModeling
          value={generalInfo.contractClinicalShift}
          onChange={(value: any) =>
            setGeneralInfo((prev: any) => ({
              ...prev,
              contractClinicalShift: value,
            }))
          }
        />
        <RebatesSection
          value={generalInfo.rebates}
          onChange={(value) =>
            setGeneralInfo((prev: any) => ({
              ...prev,
              rebates: value,
            }))
          }
        />
        <CopayModelingSection
          value={generalInfo.copayModeling}
          onChange={(value) =>
            setGeneralInfo((prev: any) => ({
              ...prev,
              copayModeling: value,
            }))
          }
        />

        <PlanExclusionsSection
          value={generalInfo.planExclusions}
          onChange={(value) =>
            setGeneralInfo((prev: any) => ({
              ...prev,
              planExclusions: value,
            }))
          }
        />

        <DawPenaltiesSection
          value={generalInfo.dawPenalties}
          onChange={(value) =>
            setGeneralInfo((prev: any) => ({
              ...prev,
              dawPenalties: value,
            }))
          }
        />

        <AdminFeesSection
          value={generalInfo.adminFees}
          onChange={(value) =>
            setGeneralInfo((prev: any) => ({
              ...prev,
              adminFees: value,
            }))
          }
        />

        <FlagsSection
          value={generalInfo.flags}
          onChange={(value) =>
            setGeneralInfo((prev: any) => ({
              ...prev,
              flags: value,
            }))
          }
        />

        <Button
          type="submit"
          colorScheme="blue"
          isLoading={isLoading}
          loadingText="Saving..."
          size="lg"
        >
          Save General Information
        </Button>
      </VStack>
    </form>
  );
}
