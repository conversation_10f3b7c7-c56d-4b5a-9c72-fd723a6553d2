import {
  Card,
  CardBody,
  CardHeader,
  FormControl,
  FormLabel,
  Heading,
  Input,
  InputGroup,
  InputLeftAddon,
  Radio,
  RadioGroup,
  SimpleGrid,
  Stack,
  VStack,
} from '@chakra-ui/react';

interface CCSModelingProps {
  value: any;
  onChange: (value: any) => void;
}

export function CCSModeling({ value, onChange }: CCSModelingProps) {
  // Default discount structures
  const defaultDiscounts: any = {
    retail30Generic: '',
    retail30Brand: '',
    retail90Generic: '',
    retail90Brand: '',
    mailGeneric: '',
    mailBrand: '',
    spGeneric: '',
    spBrand: '',
  };

  // Ensure we have a safe value with all required fields
  const safeValue: any = {
    contractClinicalShift: {
      clinicalModelingType:
        value?.contractClinicalShift?.clinicalModelingType || 'fromFile',
      discounts: value?.contractClinicalShift?.discounts
        ? { ...defaultDiscounts, ...value.contractClinicalShift.discounts }
        : defaultDiscounts,
    },
  };

  const handleContractClinicalShiftTypeChange = (newType: string) => {
    const newValue = {
      ...safeValue,
      contractClinicalShift: {
        ...safeValue.contractClinicalShift,
        clinicalModelingType: newType,
      },
    };
    onChange(newValue);
  };

  const handleContractClinicalShiftDiscountChange = (
    field: string,
    newValue: string
  ) => {
    onChange({
      ...safeValue,
      contractClinicalShift: {
        ...safeValue.contractClinicalShift,
        discounts: {
          ...safeValue.contractClinicalShift.discounts,
          [field]: newValue,
        },
      },
    });
  };

  const discountFields = [
    { key: 'retail30Generic', label: 'Retail 30 Generic' },
    { key: 'retail30Brand', label: 'Retail 30 Brand' },
    { key: 'retail90Generic', label: 'Retail 90 Generic' },
    { key: 'retail90Brand', label: 'Retail 90 Brand' },
    { key: 'mailGeneric', label: 'Mail Generic' },
    { key: 'mailBrand', label: 'Mail Brand' },
    { key: 'spGeneric', label: 'Specialty Generic' },
    { key: 'spBrand', label: 'Specialty Brand' },
  ];

  return (
    <Card>
      <CardBody>
        <VStack spacing={8} align="stretch">
          {/* Incumbent Discounts Section */}

          {/* Contract and Clinical Shift Modeling Discounts Section */}
          <Card variant="outline">
            <CardHeader>
              <Heading size="sm">
                Contract and Clinical Shift Modeling Discounts
              </Heading>
            </CardHeader>
            <CardBody>
              <VStack spacing={4} align="stretch">
                <RadioGroup
                  value={safeValue.contractClinicalShift.clinicalModelingType}
                  onChange={(newType) => {
                    handleContractClinicalShiftTypeChange(newType);
                  }}
                >
                  <Stack spacing={4}>
                    <Radio value="fromFile">
                      Use Gross Cost / Plan Cost from Incumbent File
                    </Radio>
                    <Radio value="enterDiscounts">Enter Discounts</Radio>
                    <Radio value="clinicalShiftOnly">Clinical Shift Only</Radio>
                    <Radio value="noEntry">
                      Gross Cost / Plan Cost not in file; no entry desired
                    </Radio>
                  </Stack>
                </RadioGroup>

                {safeValue.contractClinicalShift.clinicalModelingType ===
                  'enterDiscounts' && (
                  <>
                    <Heading>Dsicount Values</Heading>
                    <span>
                      Please enter discount percentages for each category
                    </span>
                    <SimpleGrid columns={2} spacing={4} mt={4}>
                      {discountFields.map((field) => (
                        <FormControl key={field.key}>
                          <FormLabel fontSize="sm">{field.label}</FormLabel>
                          <InputGroup size="sm">
                            <InputLeftAddon>%</InputLeftAddon>
                            <Input
                              type="number"
                              step="0.01"
                              placeholder="0.00"
                              max={100}
                              value={
                                safeValue.contractClinicalShift.discounts[
                                  field.key
                                ]
                              }
                              onChange={(e) => {
                                let value = parseFloat(e.target.value);
                                if (isNaN(value)) value = 0;
                                else if (value > 100) value = 100;
                                handleContractClinicalShiftDiscountChange(
                                  field.key,
                                  value.toString()
                                );
                              }}
                            />
                          </InputGroup>
                        </FormControl>
                      ))}
                    </SimpleGrid>
                  </>
                )}
              </VStack>
            </CardBody>
          </Card>
        </VStack>
      </CardBody>
    </Card>
  );
}

// Default export for the main component
export default CCSModeling;
