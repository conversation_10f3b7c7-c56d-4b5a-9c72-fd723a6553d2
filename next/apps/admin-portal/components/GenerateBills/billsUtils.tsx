import { createStandaloneToast } from '@chakra-ui/react';
import { SelectedDataRow } from '@next/shared/types';

export const toast = createStandaloneToast();

export const formatCurrency = (amount: number) =>
  `$${amount.toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,')}`;

export const formatDate = (date: Date) => {
  if (!date) return '';
  const d = new Date(date);
  const month = String(d.getUTCMonth() + 1).padStart(2, '0');
  const day = String(d.getUTCDate()).padStart(2, '0');
  const year = d.getUTCFullYear();
  return `${month}/${day}/${year}`;
};

export const formatDefaultDate = (dateString: string) => {
  if (!dateString) return undefined;
  const [year, month, day] = dateString.split('-').map(Number);
  const date = new Date(year, month - 1, day, 0, 0, 0);
  return isNaN(date.getTime()) ? undefined : date;
};

export const startOfDay = (date: any) => {
  return new Date(date.getFullYear(), date.getMonth(), date.getDate());
};

export const validateDueDate = (date: any, fieldName: string) => {
  const today = startOfDay(new Date());
  const ninetyDaysFuture = startOfDay(new Date());
  ninetyDaysFuture.setDate(today.getDate() + 90);

  if (!date) {
    return fieldName === 'monthlyDueDate'
      ? 'Typically the 20th of the month'
      : fieldName === 'quarterlyDueDate'
      ? 'Typically last Friday of the month'
      : '';
  }

  const inputDate = startOfDay(new Date(date));
  if (inputDate < today) {
    return 'Due date is in the past.';
  } else if (inputDate > ninetyDaysFuture) {
    return 'Due date is more than 90 days in the future.';
  }
  return '';
};

export const mergeRowsData = (
  rows: SelectedDataRow[]
): Record<string, string | number> => {
  const mergedData: Record<string, string | number> = {};
  const currentDate = new Date();
  currentDate.setMonth(currentDate.getMonth() - 1);
  const previousMonth = currentDate.getMonth() + 1;
  const previousYear = currentDate.getFullYear();
  const allRowsSelected = rows.length > 0;

  rows.forEach((row) => {
    console.log('bu-comBrokerNo:', row.commBrokerNo);
    console.log('bu-brokerName:', row.brokerName);
    console.log('bu-brokerCommissionNo:', row.brokerCommissionNos);
    const filteredRow: Record<string, any> = {
      year: previousYear,
      month: previousMonth,
      commBrokerNos: allRowsSelected ? row.commBrokerNo : null,
      brokerCommissionNos: allRowsSelected ? row.brokerCommissionNos : null,
    };

    Object.entries(filteredRow).forEach(([key, val]) => {
      const valueString = val != null ? val.toString() : '';
      if (!mergedData[key]) {
        mergedData[key] = valueString;
      } else {
        const existingValues = new Set(mergedData[key].toString().split(','));
        if (!existingValues.has(valueString)) {
          mergedData[key] = [...existingValues, valueString].join(',');
        }
      }
    });
  });

  return mergedData;
};
