import { createStandaloneToast } from '@chakra-ui/react';
import { useJavaApi } from '@next/shared/api';
import {
  parseDateWithoutTimezone,
  useDownloadFile,
  usePollRequest,
  useSessionStorage,
} from '@next/shared/hooks';
import {
  AggregatedData,
  BillsPaymentDetails,
  CommissionTypeData,
  SelectedDataRow,
} from '@next/shared/types';
import { useSearchParams } from 'next/navigation';
import { useCallback, useEffect, useRef, useState } from 'react';

import { formatDefaultDate } from './billsUtils';

const monthEnum: { [key: number]: string } = Object.freeze({
  0: 'January',
  1: 'February',
  2: 'March',
  3: 'April',
  4: 'May',
  5: 'June',
  6: 'July',
  7: 'August',
  8: 'September',
  9: 'October',
  10: 'November',
  11: 'December',
});

function formatMonthWithYear(month: string): string {
  let monthNumber = parseInt(month, 10);

  if (isNaN(monthNumber)) {
    throw new Error(
      `Invalid month: ${month}. Must be a string representing a number between 0 and 11. `
    );
  }

  // Ensure monthNumber is within 0-11 range
  monthNumber = Math.max(0, Math.min(monthNumber, 11));

  const currentYear = new Date().getFullYear();

  return `${monthEnum[monthNumber]} ${currentYear}`;
}

export const useBillsData = (requestNoQuery: string | string[] | undefined) => {
  const { getApi, methodApi } = useJavaApi();
  const [filteredData, setFilteredData] = useState<AggregatedData[]>([]);
  const [selectedData, setSelectedData] = useState<SelectedDataRow[]>([]);
  const [isFetchSuccessful, setIsFetchSuccessful] = useState(false);
  const [generatedBills, setGeneratedBills] = useState<AggregatedData[]>([]);
  const [billsCount, setBillsCount] = useState(0);
  const [selectionValue, setSelectionValue] = useState<string | undefined>('');
  const [commissionTypeCount, setCommissionType] = useState(0);
  const [requestNos, setRequestNos] = useState<number[]>([]);
  const handleFileDownload = useDownloadFile();
  const searchParams = useSearchParams();
  const dateMonth = new Date().getMonth().toString();
  const [batchError, setBatchError] = useState<string | null>(null);
  const { toast } = createStandaloneToast();
  const [detailedBillData, setDetailedBillData] = useState<
    Array<BillsPaymentDetails>
  >([]);
  const [unbilledCommTypeList, setUnbilledCommTypeList] = useState<
    CommissionTypeData[]
  >([]);
  const [requestStatus, setRequestStatus] = useSessionStorage(
    'commissionBillsRequestNo',
    null
  );

  const { batchProgressInfo, getRecursiveProgressStatus } = usePollRequest({
    pollRequestAPI: 'commissionPollRequestActive',
    requestType: 'CMP',
    key: requestStatus,
  });
  const fetchedDataRef = useRef(false);

  const [monthSaver, setMonthSaver] = useState<Date | undefined>(() => {
    const dateString = searchParams?.get('monthlyDueDate') as string;
    return !requestNoQuery ? formatDefaultDate(dateString) : undefined;
  });

  const [quarterlySaver, setQuarterlySaver] = useState<Date | undefined>(() => {
    const dateString = searchParams?.get('quarterlyDueDate') as string;
    return !requestNoQuery ? formatDefaultDate(dateString) : undefined;
  });

  const [yearlySaver, setYearlySaver] = useState<Date | undefined>(() => {
    const dateString = searchParams?.get('yearlyDueDate') as string;
    return !requestNoQuery ? formatDefaultDate(dateString) : undefined;
  });
  const [periodMonth, setPeriodMonth] = useState<string | undefined>(
    formatMonthWithYear(dateMonth)
  );

  const fetchPaymentDetailsList = useCallback(
    async (requestNo: number, commTypeNos: number[]) => {
      try {
        const response = await getApi('paymentDetailsList', {
          requestNo,
          commTypeNos: commTypeNos.join(','),
        });
        if (response.length > 0) {
          setDetailedBillData(response);
          fetchedDataRef.current = true;
          setRequestNos([requestNo]);
        }
      } catch (error) {
        console.error('Error fetching payment details:', error);
      }
    },
    [getApi]
  );

  const updatePaymentDetails = useCallback(
    async (requestNo: number, commTypeNos: number[]) => {
      if (!fetchedDataRef.current || !detailedBillData.length) {
        await fetchPaymentDetailsList(requestNo, commTypeNos);
      }
    },
    [detailedBillData.length, fetchPaymentDetailsList]
  );

  const fetchRequestNos = useCallback(
    async (
      paymentRequestDtos?: any,
      commTypeNos?: number[],
      rowData?: SelectedDataRow[]
    ) => {
      try {
        console.log('requestNo:', paymentRequestDtos.requestNo);
        console.log('brokerName:', paymentRequestDtos.brokerName);
        console.log(
          'brokerCommissionNo:',
          paymentRequestDtos.brokerCommissionNos
        );
        await methodApi('unbilledSummaryRequest', {
          method: 'POST',
          data: paymentRequestDtos,
          onSuccess: async (res) => {
            const requestNo = res?.payload?.requestNo;
            if (typeof requestNo !== 'undefined' && commTypeNos && rowData) {
              setRequestStatus(requestNo);
              setRequestNos([requestNo]);
              setIsFetchSuccessful(true);
              await getRecursiveProgressStatus({
                key: requestNo,
                requestType: 'CMP',
              });
              await updatePaymentDetails(requestNo, commTypeNos);
              setGeneratedBills(
                rowData.map((data) => ({ ...data, isChecked: false }))
              );
              setBillsCount(rowData.length);
              setRequestStatus(null);
            } else {
              throw new Error('Response format is not as expected');
            }
          },
          onError: (error) => console.error('Error occurred:', error),
        });
      } catch (error) {
        console.error('There was a problem with the fetch operation:', error);
      }
    },
    [
      methodApi,
      setRequestStatus,
      getRecursiveProgressStatus,
      updatePaymentDetails,
    ]
  );

  const updateSaverState = (
    parameterValue: string,
    updateStateFunction: (date: Date | undefined) => void
  ) => {
    const dateValue = parseDateWithoutTimezone(parameterValue);
    updateStateFunction(dateValue || undefined);
  };

  const fetchUnbilledCommTypeList = useCallback(async (): Promise<
    CommissionTypeData[]
  > => {
    const data = await getApi('unbilledCommTypeList');
    return data.map((item: CommissionTypeData) => ({
      ...item,
      isChecked: false,
    }));
  }, [getApi]);

  const fetchDataBillsSummary = useCallback(async () => {
    if (requestNoQuery) {
      const requestParams = await getApi('requestParms', {
        requestNo: Number(requestNoQuery),
      });
      console.log(requestParams);

      const commTypeNos = requestParams.commTypeNos;
      const brokerCommissionNos = requestParams.BrokerCommissionNos;

      const commTypeMapping: { [key in '1' | '2' | '3']: boolean } = {
        '1': !commTypeNos && !brokerCommissionNos,
        '2': !!commTypeNos && !brokerCommissionNos,
        '3': !commTypeNos && !!brokerCommissionNos,
      };

      // Find the key corresponding to true
      const commTypeKey = Object.keys(commTypeMapping).find(
        (key) => commTypeMapping[key as '1' | '2' | '3']
      );

      setSelectionValue(commTypeKey);

      updateSaverState(requestParams.MonthlyDueDate, setMonthSaver);
      updateSaverState(requestParams.QuarterlyDueDate, setQuarterlySaver);
      updateSaverState(requestParams.YearlyDueDate, setYearlySaver);
      setPeriodMonth(formatMonthWithYear(requestParams.Month));

      const data = await getApi('billedSummaryList', {
        requestNo: String(requestNoQuery),
      });
      const commTypeNoSet = new Set<number>();
      data.forEach((item: any) => {
        commTypeNoSet.add(item.commTypeNo);
      });

      const commTypeNoArray = Array.from(commTypeNoSet);

      fetchPaymentDetailsList(Number(requestNoQuery), commTypeNoArray);
      setIsFetchSuccessful(true);

      return data.map((item: AggregatedData) => ({
        ...item,
        isChecked: false,
      }));
    } else {
      const data = await fetchUnbilledCommTypeList();
      if (data) {
        setCommissionType(data.length);
        setUnbilledCommTypeList(data);
      }

      const summaryData = await getApi('unbilledSummaryList');
      const modifiedData = summaryData.map((item: AggregatedData) => {
        console.log('get-comBrokerNo:', item.commBrokerNo);
        console.log('get-brokerName:', item.brokerName);
        console.log('get-brokerCommissionNo: ', item.brokerCommissionNos);
        return {
          ...item,
          isChecked: false,
        };
      });
      return modifiedData;
    }
  }, [
    fetchPaymentDetailsList,
    fetchUnbilledCommTypeList,
    getApi,
    requestNoQuery,
  ]);

  const updateFilteredData = useCallback(async () => {
    try {
      const data = await fetchDataBillsSummary();
      setFilteredData(data);
      setBillsCount(data.length);
    } catch (error) {
      console.error('Error fetching data:', error);
    }
  }, [fetchDataBillsSummary]);

  const fetchDownload = async () => {
    const requestNo = String(requestNoQuery || requestNos[0]);
    const fileType = 'Bill';
    getApi('fileDownloadList', { requestNo })
      .then((data) =>
        handleFileDownload(
          data,
          'application/text',
          `${fileType}_${requestNo}.csv`,
          false
        )
      )
      .catch((e) => {
        console.error(e);
        toast({
          title: 'Failed to Download File',
          description: 'There was an error downloading the file.',
          status: 'error',
          duration: 3000,
        });
        setBatchError(e);
      });
  };

  useEffect(() => {
    updateFilteredData();
  }, [updateFilteredData]);

  return {
    batchError,
    periodMonth,
    filteredData,
    selectedData,
    setSelectedData,
    isFetchSuccessful,
    setIsFetchSuccessful,
    generatedBills,
    setGeneratedBills,
    billsCount,
    commissionTypeCount,
    requestNos,
    detailedBillData,
    batchProgressInfo,
    fetchedDataRef,
    updateFilteredData,
    fetchPaymentDetailsList,
    fetchRequestNos,
    fetchDownload,
    unbilledCommTypeList,
    monthSaver,
    quarterlySaver,
    yearlySaver,
    setMonthSaver,
    setQuarterlySaver,
    setYearlySaver,
    selectionValue,
  };
};
