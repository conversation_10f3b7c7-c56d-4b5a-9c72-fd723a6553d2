import {
  BaseFieldConfig,
  DropdownSelectFieldConfig,
  FieldConfig,
  InlineGroupFieldConfig,
  SubCategoryType,
} from '../../../../Models/types';
// Update import to use the default singleton instance
import zodValidations from '../utils/valdations';

type FormFieldOptions = Partial<BaseFieldConfig> & {
  optionsMap?: { [key: string]: string };
  layout?: 'row' | 'column';
  onChange?: (values: any) => void;
  allowSearch?: boolean;
  showAncillaryToggle?: boolean;
  showToggle?: boolean;
  toggleLabel?: string;
  toggleName?: string;
  toggleDefaultValue?: boolean;
  isDisabled?: boolean;
  formPath?: string;
  sync?: boolean;
};

/**
 * defineFormField
 *
 * Helper function to define a single form field.
 * Developers use this to declare a field, specify its type, name (supports dot notation),
 * initial value, and any additional options such as placeholder, isRequired, infoText,
 * and validations (a Zod schema).
 *
 * @param label - Display label for the field.
 * @param type - The type of the field (e.g., 'input', 'radioGroup', etc.).
 * @param name - Unique key for the field (supports nested structure using dot notation).
 * @param value - The initial/default value for the field.
 * @param options - Additional field options (placeholder, isRequired, infoText, validations, etc.).
 * @returns A FieldConfig object.
 */
export function defineFormField(
  label: string,
  type: Exclude<FieldConfig['type'], 'inlineGroup'>,
  name: string,
  value: any,
  options: FormFieldOptions = {}
): FieldConfig {
  // Rest of the function remains the same
  const fieldConfig: BaseFieldConfig = {
    type,
    name,
    label: label
      .replace(/_/g, ' ')
      .replace(/\b\w+/g, (word) =>
        word.length === 1 ? word : word[0].toUpperCase() + word.slice(1)
      ),
    value,
    ...options,
  };

  if (
    (type === 'radioGroup' ||
      type === 'dropdownSelect' ||
      type === 'checkboxGroup') &&
    !options.optionsMap
  ) {
    throw new Error(`optionsMap is required for ${type} fields.`);
  }

  return fieldConfig as FieldConfig;
}

/**
 * defineInlineFieldGroup
 *
 * Helper function to group multiple fields into an inline group.
 * This is useful for placing two or more related fields on the same row.
 *
 * @param fields - An array of FieldConfig objects.
 * @returns An InlineGroupFieldConfig object.
 */
export function defineInlineFieldGroup(
  fields: FieldConfig[]
): InlineGroupFieldConfig {
  return { type: 'inlineGroup', fields };
}

/**
 * defineSubCategory
 *
 * Helper function to define a subcategory (a section) of the form.
 * A subcategory includes a title, description, and an array of fields (or inline groups).
 *
 * @param title - The section title.
 * @param description - A description of the section.
 * @param fields - Array of FieldConfig or InlineGroupFieldConfig objects.
 * @returns A SubCategoryType object.
 */
export function defineSubCategory(
  title: string,
  description: string,
  fields: (FieldConfig | InlineGroupFieldConfig)[]
): SubCategoryType {
  return { title, description, fields };
}

export function createDynamicGroups<T>(
  groups: T[],
  prefix: string,
  updateGroup?: (group: T, index: number) => T
): Record<string, T> {
  return groups.reduce((acc, group, idx) => {
    const updatedGroup = updateGroup ? updateGroup(group, idx) : group;
    const key = `${prefix}${idx + 1}`;
    acc[key] = updatedGroup;
    return acc;
  }, {} as Record<string, T>);
}

/**
 * Helper function to get a path to a property
 */
export function getPropertyPath(basePath: string, property: string): string {
  return `${basePath}.${property}`;
}

/**
 * Helper function to get a path to an array item
 */
export function getArrayItemPath(basePath: string, index: number): string {
  return `${basePath}[${index}]`;
}

/**
 * Helper function to get a base path based on whether it's a new item or existing
 */
export function getBasePath(
  isNewItem: boolean,
  path: string,
  index = -1
): string {
  return isNewItem ? 'temp' : index >= 0 ? getArrayItemPath(path, index) : path;
}

/** Helper function to replace placeholder in paths
 */
export function replacePlaceholder(
  path: string,
  placeholderOrIndex: string | number,
  value?: string
): string {
  // Case 1: Using the index replacement pattern (original first function)
  if (typeof placeholderOrIndex === 'number' && value === undefined) {
    return path.replace('{i}', placeholderOrIndex.toString());
  }

  // Case 2: Using the generic placeholder replacement pattern (original second function)
  if (typeof placeholderOrIndex === 'string' && value !== undefined) {
    return path.replace(placeholderOrIndex, value);
  }

  // Handle invalid usage
  throw new Error('Invalid parameters for replacePlaceholder function');
}

/**
 * Helper function to generate a paths object with placeholders replaced
 */
export function generatePaths<T extends Record<string, string>>(
  config: T,
  index: number
): Record<keyof T, string> {
  const result = {} as Record<keyof T, string>;
  const iStr = index.toString();

  for (const key of Object.keys(config) as (keyof T)[]) {
    result[key] = config[key].replace('{i}', iStr);
  }

  // Cast back to original type
  return result as T;
}

/**
 * Helper function to create a string field with Zod validation
 */
export function defineStringField(
  label: string,
  name: string,
  value: string | null,
  options: Partial<BaseFieldConfig> & {
    min?: number;
    max?: number;
    pattern?: RegExp;
    message?: string;
  } = {}
): FieldConfig {
  // Create a Zod schema for string validation - updated to use the instance methods
  const validations = zodValidations.string({
    required: options.isRequired,
    min: options.min,
    max: options.max,
    pattern: options.pattern,
    message: options.message,
  });

  return defineFormField(label, 'input', name, value, {
    ...options,
    validations,
  });
}

/**
 * Helper function to create a number field with Zod validation
 */
export function defineNumberField(
  label: string,
  name: string,
  value: number | null,
  options: Partial<BaseFieldConfig> & {
    min?: number;
    max?: number;
    int?: boolean;
    message?: string;
  } = {}
): FieldConfig {
  // Create a Zod schema for number validation - updated to use the instance methods
  const validations = zodValidations.number({
    required: options.isRequired,
    min: options.min,
    max: options.max,
    int: options.int,
    message: options.message,
  });

  // For integer fields, add a pattern attribute to prevent 'e' input
  const additionalProps: Record<string, any> = {};

  if (options.int) {
    additionalProps.pattern = '[0-9]*';
    additionalProps.inputMode = 'numeric';

    // Also add a custom validator function to check for integers
    additionalProps.customValidate = (value: any) => {
      if (value === null || value === undefined || value === '') {
        return options.isRequired ? 'This field is required' : true;
      }

      const num = Number(value);
      if (isNaN(num) || !Number.isInteger(num)) {
        return options.message || 'Must be an integer';
      }

      return true;
    };
  }

  return defineFormField(label, 'input', name, value, {
    ...options,
    validations,
    ...additionalProps,
  });
}

/**
 * Helper function to create a dropdown field with Zod validation
 */
export function defineDropdownField<T extends string | number>(
  label: string,
  name: string,
  value: T | null,
  optionsMap: Record<string | number, string>,
  options: Partial<DropdownSelectFieldConfig> & {
    message?: string;
    formPath?: string;
    sync?: boolean;
  } = {}
): FieldConfig {
  // Get all valid values from the options map
  const validValues = Object.keys(optionsMap);

  // Create appropriate Zod schema based on the key type
  const validations =
    typeof value === 'number'
      ? zodValidations.numericSelect(
          validValues.map((v) => Number(v)),
          { required: options.isRequired, message: options.message }
        )
      : zodValidations.enum.stringEnum(validValues, {
          required: options.isRequired,
          message: options.message,
        });

  return defineFormField(label, 'dropdownSelect', name, value, {
    ...options,
    optionsMap,
    validations,
  });
}

/**
 * Helper function to create a number dropdown field with Zod validation
 */
export function defineNumberDropdownField(
  label: string,
  name: string,
  value: number | null,
  optionsMap: Record<string | number, string>,
  options: Partial<DropdownSelectFieldConfig> & {
    message?: string;
  } = {}
): FieldConfig {
  // Get all valid numeric values from the options map
  const validValues = Object.keys(optionsMap).map((k) => Number(k));

  // Create a Zod schema for number enum validation - updated to use the instance methods
  const validations = zodValidations.numericSelect(validValues, {
    required: options.isRequired,
    message: options.message,
  });

  return defineFormField(label, 'dropdownSelect', name, value, {
    ...options,
    optionsMap,
    validations,
  });
}

/**
 * Helper function to create a phone field with Zod validation
 */

export function definePhoneField(
  label: string,
  name: string,
  value: string | null,
  options: Partial<BaseFieldConfig> & {
    format?: 'us' | 'international' | 'formatted';
    message?: string;
  } = {}
): FieldConfig {
  // Create a Zod schema for phone validation - updated to use the instance methods
  const validations = zodValidations.phone({
    required: options.isRequired,
    format: options.format, // Use format instead of pattern
    message: options.message || 'Please enter a valid phone number',
  });

  return defineFormField(label, 'input', name, value, {
    ...options,
    validations,
    placeholder: options.placeholder || 'Enter phone number',
  });
}
