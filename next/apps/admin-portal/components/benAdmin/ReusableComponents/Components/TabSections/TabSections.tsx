import {
  <PERSON>,
  Tab,
  <PERSON>b<PERSON>ist,
  TabPanel,
  TabPanels,
  Tabs,
  Tag,
  Text,
  useDisclosure,
  useToast,
} from '@chakra-ui/react';
import { useSaveChangeRequestHandler } from 'apps/admin-portal/components/benAdmin';
import React from 'react';

import { TabSectionProps } from '../../Models/types';
import { useFormCompletionTracking } from '../Form/components/hooks/Completion/useFormCompletion';
import { SimpleDropdown } from '../Form/components/SimpleDropdown';
import PublishConfirmModal from './PublishModal';

// Fields to check for non-null values in ancillaries
const DATA_FIELDS = [
  'effective_date',
  'expiration_date',
  'eligibility_export_ind',
  'pharmacy_claim_export_ind',
  'notes',
];

// Helper: Clean plan_design_ancillaries before publishing
function cleanAncillariesForPublish(formData: any): any {
  if (!formData?.plan?.plan_designs) return formData;
  const cleanedPlanDesigns = formData.plan.plan_designs.map((plan: any) => {
    if (!Array.isArray(plan.plan_design_ancillaries)) return plan;
    const filteredAncillaries = plan.plan_design_ancillaries.filter(
      (anc: any) => {
        return DATA_FIELDS.some(
          (key) =>
            anc[key] !== null && anc[key] !== undefined && anc[key] !== ''
        );
      }
    );
    return {
      ...plan,
      plan_design_ancillaries: filteredAncillaries,
    };
  });
  return {
    ...formData,
    plan: {
      ...formData.plan,
      plan_designs: cleanedPlanDesigns,
    },
  };
}

const TabSection: React.FC<TabSectionProps> = ({
  tabs,
  isGuided = false,
  formMethods,
}) => {
  const { overallCompletion } = useFormCompletionTracking();

  const {
    isOpen: isPublishModalOpen,
    onOpen: onPublishModalOpen,
    onClose: onPublishModalClose,
  } = useDisclosure();
  const toast = useToast();
  const publishHandler = useSaveChangeRequestHandler(
    formMethods || ({} as any),
    true
  );
  const isDirty = formMethods?.formState.isDirty || false;

  const renderHeader = () => (
    <Box
      display="flex"
      flexDirection="row"
      justifyContent="space-between"
      alignItems="center"
      mb={4}
    >
      <Box display="flex" justifyContent="space-between" alignItems="center">
        <Text fontSize="20px" fontWeight="bold" mr={4}>
          Plan Design
        </Text>
        <Box display="flex">
          {overallCompletion === 100 ? (
            <Tag colorScheme="green" size="sm">
              Published
            </Tag>
          ) : (
            <Tag colorScheme="orange" size="sm">
              In Progress
            </Tag>
          )}
        </Box>
      </Box>
      <SimpleDropdown
        placeholder="Actions"
        optionsMap={{
          'Publish Plan Design': 'Publish Plan Design',
        }}
        onChange={(selectedKey: string) => {
          if (selectedKey === 'Publish Plan Design') {
            onPublishModalOpen();
          }
        }}
        value=""
        width="200px"
      />
    </Box>
  );

  return (
    <Tabs variant="enclosed" mt={12}>
      <TabList display="inline-flex" borderBottom="2px solid #008750">
        {tabs.map((tab, index) => (
          <Box
            key={tab.label || index}
            borderTopLeftRadius="2xl"
            borderTopRightRadius="2xl"
            bg="white"
            ml={index > 0 ? 2 : 0}
          >
            <Tab
              _selected={{ color: 'white', bg: '#008750' }}
              textColor="#008750"
              borderTopLeftRadius="2xl"
              borderTopRightRadius="2xl"
            >
              <Text fontSize="md" fontWeight="normal">
                {tab.label}
              </Text>
            </Tab>
          </Box>
        ))}
      </TabList>

      <TabPanels mt={0.5}>
        {tabs.map((tab, index) => (
          <Box
            bg="white"
            key={tab.label || index}
            borderBottomLeftRadius="md"
            borderBottomRightRadius="md"
            borderTopRightRadius="md"
          >
            <TabPanel p={4}>
              {' '}
              {index === 0 && isGuided && renderHeader()}
              {tab?.component}
            </TabPanel>
          </Box>
        ))}
      </TabPanels>

      <PublishConfirmModal
        isOpen={isPublishModalOpen}
        onClose={onPublishModalClose}
        onPublish={async () => {
          if (!formMethods) return;

          let isValid = false;
          try {
            isValid = await formMethods.trigger();
            let formData = formMethods.getValues();

            if (isValid) {
              formData = cleanAncillariesForPublish(formData);
              await publishHandler(formData);

              toast({
                title: 'Plan Design published successfully',
                status: 'success',
                duration: 5000,
                isClosable: true,
              });

              onPublishModalClose();
            } else {
              toast({
                title: 'Validation Error',
                description:
                  'Errors found during validation. Please fix the errors and try again.',
                status: 'error',
                duration: 5000,
                isClosable: true,
              });
            }
          } catch (error: any) {
            console.error('Publishing failed:', error);
            toast({
              title: 'Publishing Failed',
              description:
                error?.message ||
                'An unexpected error occurred while publishing. Please try again.',
              status: 'error',
              duration: 5000,
              isClosable: true,
            });
            // TODO: Do not close modal on error?
          }
        }}
        isPublishDisabled={!isDirty}
      />
    </Tabs>
  );
};

export default TabSection;
