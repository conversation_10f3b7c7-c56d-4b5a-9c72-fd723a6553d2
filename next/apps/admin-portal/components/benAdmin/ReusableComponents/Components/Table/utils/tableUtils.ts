import { PlanDocument } from 'apps/admin-portal/components/benAdmin/Models/interfaces';

export const formatHeader = (header: string) =>
  header === 'exemptedHeader'
    ? null
    : header
        ?.split('_')
        .map(
          (word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
        )
        .join(' ');

export const formatValue = (value: unknown, datatype: string): string => {
  if (value === null || value === undefined) return '—';

  switch (datatype) {
    case 'dollar':
      return formatDollar(value);
    case 'date':
      return formatDate(value) || '';
    case 'number':
      return formatNumber(value);
    case 'boolean':
      return value === (true || 'true' || 1 || '1')
        ? 'Yes'
        : value === (false || 'false' || 0 || '0')
        ? 'No'
        : '—';
    default:
      return String(value);
  }
};

export const formatDollar = (value: unknown): string => {
  if (typeof value === 'number') {
    return `$${value.toFixed(2)}`;
  }
  const parsedValue = parseFloat(String(value));
  return isNaN(parsedValue) ? '—' : `$${parsedValue.toFixed(2)}`;
};

export const setEffectiveStatus = (
  value: unknown,
  ind: unknown
): string | undefined => {
  if (!value || ind == null) return undefined;
  if (
    typeof value !== 'string' &&
    typeof value !== 'number' &&
    !(value instanceof Date)
  ) {
    return 'Invalid Date';
  }
  const expDate = new Date(value);
  const currentDate = new Date();

  expDate.setHours(0, 0, 0, 0);
  currentDate.setHours(0, 0, 0, 0);

  if (expDate >= currentDate && ind === 1) {
    return 'Implementation Termed';
  } else if (ind === 0) {
    return 'Inactive';
  } else return 'active';
};

export const formatDate = (
  value: unknown,
  formatISO = false
): string | undefined => {
  if (!value) return undefined;
  if (
    typeof value !== 'string' &&
    typeof value !== 'number' &&
    !(value instanceof Date)
  ) {
    return 'Invalid Date';
  }

  const hasTimeComponent = typeof value === 'string' && value.includes('T');

  let date: Date;
  if (hasTimeComponent) {
    date = new Date(value);

    // Get the user's local time offset in hours
    const localOffset = new Date().getTimezoneOffset() / 60; // Timezone offset in hours (negative for behind UTC)

    // Adjust the date by subtracting the local offset
    date.setHours(date.getHours() - localOffset);
  } else if (typeof value === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(value)) {
    const [year, month, day] = value.split('-').map(Number);
    date = new Date(year, month - 1, day);
  } else {
    date = new Date(value);
  }

  if (Number.isNaN(date.getTime())) return 'Invalid Date';

  // If formatISO is true, return ISO format (YYYY-MM-DD)
  if (formatISO) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    // If there's a time component, include it in ISO format
    if (hasTimeComponent) {
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}`;
    }

    // Otherwise just return the date part
    return `${year}-${month}-${day}`;
  }

  // Default formatting (original behavior)
  const options: Intl.DateTimeFormatOptions = {
    month: '2-digit',
    day: '2-digit',
    year: 'numeric',
    ...(hasTimeComponent && {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    }),
  };

  // Format the date as local time
  return date
    .toLocaleDateString('en-US', options)
    .replace(/,/g, hasTimeComponent ? ' at' : '');
};

const formatNumber = (value: unknown): string => {
  if (typeof value === 'number') {
    return value.toLocaleString();
  }
  const parsedValue = parseFloat(String(value));
  return isNaN(parsedValue) ? '—' : parsedValue.toLocaleString();
};

export const getNestedValue = (
  obj: any,
  key: string,
  defaultValue: any = ''
) => {
  const keys = key.split('.'); // Split key by dot notation

  const result = keys.reduce((value, currentKey) => {
    if (value === undefined || value === null) return undefined;
    return value[currentKey];
  }, obj);

  return result !== undefined ? result : defaultValue;
};

export const setNestedValue = (obj: any, key: string, value: any): any => {
  const keys = key.split('.');
  const lastKey = keys.pop();

  if (!lastKey) return obj;

  const nested = keys.reduce((acc, currentKey) => {
    if (!acc[currentKey] || typeof acc[currentKey] !== 'object') {
      acc[currentKey] = {}; // Initialize nested objects if they don't exist
    }
    return acc[currentKey];
  }, obj);

  nested[lastKey] = value;
  return obj;
};

export const flattenObjectKeys = (
  obj: any,
  prefix = '',
  result: Record<string, any> = {}
): Record<string, any> => {
  // If obj is null/undefined or not an object, just return existing result.
  if (!obj || typeof obj !== 'object') {
    return result;
  }

  Object.keys(obj).forEach((key) => {
    const value = obj[key];
    const newKey = prefix ? `${prefix}.${key}` : key;

    if (value && typeof value === 'object' && !Array.isArray(value)) {
      // Recursively flatten nested objects
      flattenObjectKeys(value, newKey, result);
    } else {
      // If it's a leaf value, store it in result.
      const adjustedKey = newKey.includes('.')
        ? newKey.split('.').pop()!
        : newKey;
      result[adjustedKey] = value;
    }
  });

  return result;
};

/**
 * Compares two objects to determine if there are any differences.
 * Returns true if differences exist, otherwise false.
 *
 * @param obj1 - The first object to compare.
 * @param obj2 - The second object to compare.
 * @returns {boolean} - True if objects differ, otherwise false.
 */
export function shouldTriggerUpdate<T>(obj1: T, obj2: T): boolean {
  const isObject = (value: any): boolean =>
    value && typeof value === 'object' && !Array.isArray(value);

  const deepCompare = (o1: any, o2: any): boolean => {
    if (o1 === o2) return false; // No difference if values are strictly equal
    if (!isObject(o1) || !isObject(o2)) return true; // Primitive values differ

    // Compare keys
    const o1Keys = Object.keys(o1);
    const o2Keys = Object.keys(o2);
    if (o1Keys.length !== o2Keys.length) return true; // Different number of keys

    // Recursively compare values
    return o1Keys.some((key) => deepCompare(o1[key], o2[key]));
  };

  return deepCompare(obj1, obj2);
}
export const truncateString = (str: string, maxLength: number): string => {
  return str.length > maxLength ? str.substring(0, maxLength) + '...' : str;
};

/**
 * Returns PlanDocuments where only the most recent PDX, PBC, or XML is shown in the array
 *
 * @param documents - Array of all documents associated with a plan
 * @returns {PlanDocument[]} - Array of PlanDocuments with no duplicate PDX, PBC, or XML files (i.e., only the most recent PDX is shown, if applicable)
 */
export const showMostRecentGeneratedDocument = (documents: PlanDocument[]) => {
  if (!documents) return [];
  const filterPDX = 'pdx';
  const filterPBC = 'pbc';
  const filterXML = 'esi_xml';
  const result: PlanDocument[] = [];
  const seen = new Map();

  documents?.forEach((doc) => {
    if (
      doc.file_name.includes(filterPDX) ||
      doc.file_name.includes(filterPBC) ||
      doc.file_name.includes(filterXML)
    ) {
      if (
        !seen.has(doc.file_name) ||
        new Date(doc.created_date) >
          new Date(seen.get(doc.file_name).created_date)
      ) {
        seen.set(doc.file_name, doc);
      }
    } else {
      result.push(doc);
    }
  });

  for (const doc of documents) {
    if (
      (doc.file_name.includes(filterPDX) ||
        doc.file_name.includes(filterPBC) ||
        doc.file_name.includes(filterXML)) &&
      seen.has(doc.file_name)
    ) {
      result.push(seen.get(doc.file_name));
      seen.delete(doc.file_name);
    }
  }

  result.sort(
    (a, b) =>
      new Date(b.created_date).getTime() - new Date(a.created_date).getTime()
  );

  return result;
};
