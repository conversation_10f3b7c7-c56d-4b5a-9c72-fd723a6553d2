import React, { ReactNode } from 'react';
import {
  FieldValues,
  useForm,
  UseFormRegister,
  UseFormReturn,
} from 'react-hook-form';
import { z } from 'zod';

import {
  LegalEntityAssignments,
  OrganizationDetails,
} from '../../Models/interfaces';
import { PicklistMap } from '../../organization/maps/picklistInterface';
// ----------------------------
// Utility Types for AutoTable
// ----------------------------
export type SortDirection = 'asc' | 'desc' | null;

export type NestedKeys<
  T,
  Prefix extends string = '',
  Depth extends number = 3
> = [Depth] extends [never]
  ? never
  : {
      [K in Extract<keyof T, string>]: T[K] extends object
        ? NestedKeys<T[K], `${Prefix}${K}.`, PrevDepth<Depth>> | `${Prefix}${K}`
        : `${Prefix}${K}`;
    }[Extract<keyof T, string>];

type PrevDepth<N extends number> = N extends 1
  ? never
  : N extends 2
  ? 1
  : N extends 3
  ? 2
  : never;

// ----------------------------
// Base Field Type
// ----------------------------
export interface BaseFieldConfig {
  type:
    | 'input'
    | 'radioGroup'
    | 'dropdownSelect'
    | 'checkboxGroup'
    | 'datepicker'
    | 'text';
  name: string; // Name property to map to backend keys
  label: string;
  value: any;
  placeholder?: string;
  isRequired?: boolean;
  infoText?: string;
  validations?: z.ZodTypeAny;
}

// ----------------------------
// Specific Field Configurations
// ----------------------------
export interface InputFieldConfig extends BaseFieldConfig {
  type: 'input';
  isDisabled?: boolean;
  showAncillaryToggle?: boolean;
  showToggle?: boolean;
  toggleLabel?: string;
  toggleName?: string;
  toggleDefaultValue?: boolean;
  formPath?: string;
  sync?: boolean;
  idMap?: {
    key: string;
    value: number;
  } | null;
  syncFunction?: (
    sync: boolean,
    toggleValue: boolean,
    name: string,
    value: string | number,
    formMethods: UseFormReturn<any>
  ) => void;
}

export interface TextFieldConfig extends BaseFieldConfig {
  type: 'text';
}

export interface RadioGroupFieldConfig extends BaseFieldConfig {
  type: 'radioGroup';
  optionsMap: { [key: string]: string };
  inputField?: { onChange: (value: string) => void };
  layout?: 'row' | 'column';
}

export interface DropdownSelectFieldConfig extends BaseFieldConfig {
  type: 'dropdownSelect';
  optionsMap: { [key: string | number]: string } | PicklistMap;
  allowSearch?: boolean;
  onChange?: (key: string | null) => void;
  isDisabled?: boolean;

  // Toggle related properties
  showAncillaryToggle?: boolean;
  showToggle?: boolean;
  toggleLabel?: string;
  toggleName?: string;
  toggleDefaultValue?: boolean;
  formPath?: string;
  sync?: boolean;
  idMap?: {
    key: string;
    value: number;
  } | null;
  syncFunction?: (
    sync: boolean,
    toggleValue: boolean,
    name: string,
    value: string | number,
    formMethods: UseFormReturn<any>
  ) => void;
}

export interface CheckboxGroupFieldConfig extends BaseFieldConfig {
  type: 'checkboxGroup';
  optionsMap: { [key: string]: string }; // Options for checkboxes
  selectedValues?: string[];
  layout?: 'row' | 'column'; // Layout option for checkbox group
  onChange?: (values: string[]) => void;
}

export interface DatepickerFieldConfig extends BaseFieldConfig {
  type: 'datepicker';
  dateFormat?: string;
  minDate?: string;
  maxDate?: string;
  onChange?: (date: string) => void;
  isDisabled?: boolean;
  showAncillaryToggle?: boolean;
  showToggle?: boolean;
  toggleLabel?: string;
  toggleName?: string;
  toggleDefaultValue?: boolean;
  formPath?: string;
}

export interface InlineGroupFieldConfig {
  type: 'inlineGroup';
  fields: FieldConfig[];
}

// ----------------------------
// Union Type for All Field Configurations
// ----------------------------
export type FieldConfig =
  | InputFieldConfig
  | TextFieldConfig
  | RadioGroupFieldConfig
  | DropdownSelectFieldConfig
  | CheckboxGroupFieldConfig
  | DatepickerFieldConfig
  | InlineGroupFieldConfig;

export type TemplateFieldConfig = Exclude<FieldConfig, InlineGroupFieldConfig>;

type FormChangeHandler = (
  name: string,
  value: string | number | string[]
) => void;

// ----------------------------
// Field Group Configuration
// ----------------------------
export type TemplateFieldGroup<T extends FieldValues = any> = {
  subtitle: string;
  fields: Partial<TemplateFieldConfig>[];
  columns?: number;
  handleSubmit?: any;
  onCancel?: any;
  handleFormChange?: FormChangeHandler;
  editable?: boolean;
  formMethods: UseFormReturn<T>;
  register: UseFormRegister<any>;
  tab?: string;
};

export type FieldGroup = {
  subtitle: string;
  fields: FieldConfig[];
  columns?: number;
  onSubmit?: FormChangeHandler;
  handleFormChange?: FormChangeHandler;
  editable?: boolean;
  formMethods: ReturnType<typeof useForm>;
};

// ----------------------------
// Template Configuration
// ----------------------------
export type TemplateItem = {
  title: string;
  content?: React.ReactNode;
  fieldGroups?: FieldGroup[];
};

type BaseTemplateProps = {
  panelPadding?: string | number;
  backgroundColor?: string;
  titleColor?: string;
};

type MultipleTemplateProps = BaseTemplateProps & {
  items: TemplateItem[];
  title?: never;
  content?: never;
};

type SingleTemplateProps = BaseTemplateProps & {
  items?: TemplateItem[];
  title: string;
  content?: React.ReactNode;
};

export type TemplateProps = MultipleTemplateProps | SingleTemplateProps;

// ----------------------------
// AutoTable Types
// ----------------------------
export interface CellDisplay {
  value?: any;
  display?: string | number | React.ReactNode;
  datatype?: string;
  style?: React.CSSProperties;
}

export type ColumnSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';

export type AutoTableColumn<T> = {
  key: string;
  override?: {
    headerName?: string;
    datatype?: string;
    data?: ((row: T) => CellDisplay) | CellDisplay | string;
    disableRow?: (row: T) => boolean;
    style?: React.CSSProperties | ((row: T) => React.CSSProperties);
    columnSize?: ColumnSize;
    accordion?: boolean;
  };
};

export type AutoTableColumns<T> = AutoTableColumn<T>[];

export interface AutoTableProps<T> {
  data: (T & { disableRow?: boolean })[];
  columns: AutoTableColumn<T>[];
  searchConfig?: {
    placeholder: string;
    searchField: keyof T;
    label?: string;
    hint?: string;
    disabled?: boolean;
    quickSearchOptions?: {
      label: string;
      filterFunction: (data: T[]) => T[];
      active?: boolean;
    }[];
    defaultQuickFilter?: string;
  };
  onRowClick?: (row: T) => void;
}

export interface SortState {
  column: string | null;
  direction: 'asc' | 'desc' | null;
}

// Updated SubCategoryType
export interface SubCategoryType<T extends FieldValues = any> {
  title: string;
  description: string;
  formMethods?: UseFormReturn<T>;
  fields: FieldConfig[]; // Array of fields, including inline groups
}

export interface TabItem {
  label: string;
  component?: React.ReactNode;
}

export interface TabSectionProps {
  tabs: TabItem[];
  isGuided?: boolean;
  formMethods: UseFormReturn<any>;
}

// TypeScript interfaces
interface DropdownItem {
  id: string;
  label: string;
  component?: ReactNode;
}

export interface MenuItem {
  id: string;
  label: string;
  icon?: any;
  component?: ReactNode;
  hasDropdown?: boolean;
  dropdownItems?: DropdownItem[];
  disabled?: boolean;
}

export interface SidebarConfig {
  sections: {
    title: string;
    items: SidebarItem[];
  }[];
}

export interface SideNavigationBarProps {
  config: SidebarConfig;
  onSelectItem?: (item: MenuItem | DropdownItem) => void;
  activeItemId?: string;
}

export interface SidebarItem {
  id: string;
  label: string;
  icon?: any;
  component?: React.ReactNode;
  disabled?: boolean;
  hasDropdown?: boolean;
  dropdownItems?: SidebarItem[];
  modeChange?: string; // Indicates this item triggers a mode switch
}
interface ModeConfig {
  getSidebarConfig: (
    organizationDetails: OrganizationDetails,
    formMethods: UseFormReturn<any>,
    navigateFn?: (section: string, tab?: string) => void
  ) => SidebarConfig;
  defaultItemId: string;

  urlParams?: UrlParamsConfig;
}

export interface NavigationConfig {
  baseUrl: string;
  organizationDetails: OrganizationDetails;
  formMethods: UseFormReturn<any>;
  modes: {
    [modeName: string]: ModeConfig;
  };
  defaultMode: string; // e.g., 'main'
}

export interface NavigationState {
  currentMode: string;
  activeItem: string;
  currentComponent: React.ReactNode;
}

/**
 * Configuration for URL parameter preservation
 */
export interface UrlParamsConfig {
  // Parameters to preserve when navigating (empty array means preserve none)
  preserveParams?: string[];

  // Optional transformation function for parameters
  transformParams?: (params: URLSearchParams) => URLSearchParams;
}

type NullableFields<T> = {
  [K in keyof T]: T[K] extends Date | null
    ? Date | string | null | undefined
    : T[K] extends Date
    ? Date | string | undefined
    : T[K] extends number | null
    ? number | null | undefined
    : T[K] extends number
    ? number | null | undefined
    : T[K] extends boolean
    ? boolean | undefined
    : T[K] extends string
    ? string | undefined
    : T[K] extends object
    ? NullableFields<T[K]> | undefined
    : T[K] | undefined;
};

export type NullableLegalEntitys = NullableFields<LegalEntityAssignments>;
