import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import React, { useMemo } from 'react';

import { useOrganizationHandlers } from '../../../../hooks/useOrganizationHandlers';
import { DocumentCenter } from '../../../Tabs/DocumentCenter/DocumentCenter';

const DocumentCenterComponent: React.FC<{
  changeRequest: OrganizationDetails;
}> = ({ changeRequest }) => {
  const organizationDetails = useMemo(
    () => changeRequest as OrganizationDetails,
    [changeRequest]
  );

  const { formMethods } = useOrganizationHandlers({ organizationDetails });
  return (
    <DocumentCenter
      planId={changeRequest?.plan.plan_id}
      formMethods={formMethods}
    />
  );
};

export default DocumentCenterComponent;
