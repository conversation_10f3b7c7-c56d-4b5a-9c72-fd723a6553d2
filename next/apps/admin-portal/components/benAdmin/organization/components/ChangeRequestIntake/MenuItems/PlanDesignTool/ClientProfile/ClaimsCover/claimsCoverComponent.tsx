import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { useSaveChangeRequestHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/useOrganizationHooks';
import GenericForm from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/GenericForm';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import {
  ELIGIBILITY_ITEM,
  FSA_HRA_HSA_ITEM,
} from '../../../../Navigation/navigationConstants';
import { useClaimsCoverForm } from './claimsCoverForm';

interface ClaimsCoverComponentProps {
  formMethods: UseFormReturn<any>;
  onUpdateActiveItem?: (id: string) => void;
}

const ClaimsCoverComponent: React.FC<ClaimsCoverComponentProps> = ({
  formMethods,
  onUpdateActiveItem,
}) => {
  const { watch } = formMethods;
  const currentDetails = watch();

  const submitHandler = useSaveChangeRequestHandler(formMethods);

  const handleSaveAndExit = () => {
    const currentValues = formMethods.getValues();
    submitHandler(currentValues);
  };

  const { subCategories } = useClaimsCoverForm(
    currentDetails as Partial<OrganizationDetails>
  );

  const handleBack = () => {
    if (onUpdateActiveItem) {
      onUpdateActiveItem(ELIGIBILITY_ITEM);
    }
  };

  const handleContinue = () => {
    if (onUpdateActiveItem) {
      onUpdateActiveItem(FSA_HRA_HSA_ITEM);
    }
  };

  return (
    <GenericForm
      formMethods={formMethods}
      formName="Claims Cover"
      formDescription=""
      subCategories={subCategories}
      onContinue={handleContinue}
      onBack={handleBack}
      onSaveExit={handleSaveAndExit}
    />
  );
};

export default ClaimsCoverComponent;
