import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { useSaveChangeRequestHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/useOrganizationHooks';
import GenericForm from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/GenericForm';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import { useClientInformationForm } from './clientInformationForm';

interface ClientInformationComponentProps {
  formMethods: UseFormReturn<any>;
  onUpdateActiveItem?: (id: string) => void;
}

const ClientInformationComponent: React.FC<ClientInformationComponentProps> = ({
  formMethods,
  onUpdateActiveItem,
}) => {
  const { watch } = formMethods;
  const currentDetails = watch();

  // Build form subCategories and get the original continue/back handlers.
  const { subCategories } = useClientInformationForm(
    currentDetails as Partial<OrganizationDetails>
  );
  const submitHandler = useSaveChangeRequestHandler(formMethods);

  // When the user clicks "Continue", run any internal continue logic then update the active menu item.
  const handleContinue = () => {
    // Seamlessly update the active menu item to "pharmacy-benefits-manager" via the callback.
    if (onUpdateActiveItem) {
      onUpdateActiveItem('pharmacy-benefits-manager');
    }
  };

  const handleSaveAndExit = () => {
    // Submit the current form data
    const currentValues = formMethods.getValues();
    submitHandler(currentValues);
  };

  return (
    <GenericForm
      formMethods={formMethods}
      formName="Client Information"
      formDescription=""
      subCategories={subCategories}
      onContinue={handleContinue}
      onSaveExit={handleSaveAndExit}
    />
  );
};

export default ClientInformationComponent;
