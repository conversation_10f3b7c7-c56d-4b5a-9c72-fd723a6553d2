import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { clientConfig } from 'apps/admin-portal/components/benAdmin/organization/components/Tabs/PlanDesign/ClientProfile/Config/clientConfig';
import { usePicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistMaps';
import {
  defineFormField,
  defineInlineFieldGroup,
  defineSubCategory,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';
import { SubCategoryType } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import { z } from 'zod';

/**
 * useClientInformationForm Hook
 *
 * Generates form subcategories (sections) based on the provided organization details.
 * Uses dot notation for field names so that the final form JSON is nested.
 *
 * @param currentDetails - Partial organization data used to prefill form fields.
 * @returns An object containing the generated subCategories.
 */
export function useClientInformationForm(
  currentDetails: Partial<OrganizationDetails>
) {
  const {
    erisaMap,
    benefitPeriodsMap,
    terminationReasonMap,
    monthsMap,
    planClassMap,
    stateMap,
    yesNoMap,
  } = usePicklistMaps();

  // Build the subcategories using the helper functions.
  const subCategories: SubCategoryType[] = [
    defineSubCategory('', '', [
      defineInlineFieldGroup([
        defineFormField(
          'Legal Name',
          'input',
          clientConfig.legal_entity_name,
          currentDetails?.organization?.legal_entity_name,
          {
            placeholder: 'Enter legal name',
            isRequired: true,
            validations: z
              .string({ required_error: 'Legal Name is required.' })
              .min(5, 'Organization name must be at least 5 characters')
              .max(100, 'Organization name must be less than 100 characters'),
          }
        ),
        defineFormField(
          'Employer Address',
          'input',
          clientConfig.address_line_1,
          currentDetails?.organization?.address?.address_line_1,
          {
            placeholder: 'Enter employer address',
            isRequired: true,
            validations: z
              .string({ required_error: 'Employer Address is required.' })
              .min(1, 'Employer Address cannot be empty.')
              .max(255, 'Address must be less than 255 characters'),
          }
        ),

        defineFormField(
          'Employer Address 2',
          'input',
          clientConfig.address_line_2,
          currentDetails?.organization?.address?.address_line_2,
          {
            placeholder: 'Enter employer address 2',
            isRequired: false,
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Employer City',
          'input',
          clientConfig.city,
          currentDetails?.organization?.address?.city,
          {
            placeholder: 'Enter employer city',
            isRequired: false,
          }
        ),
        defineFormField(
          'Employer State',
          'dropdownSelect',
          clientConfig.state,
          currentDetails?.organization?.address?.state,
          {
            placeholder: 'Enter employer state',
            isRequired: false,
            optionsMap: stateMap,
          }
        ),
        defineFormField(
          'Employer Zip',
          'input',
          clientConfig.postal_code,
          currentDetails?.organization?.address?.postal_code,
          {
            placeholder: 'Enter employer zip',
            isRequired: false,
          }
        ),
      ]),
    ]),
    defineSubCategory('', '', [
      defineInlineFieldGroup([
        defineFormField(
          'Benefit Period',
          'dropdownSelect',
          clientConfig.benefit_period_ind,
          currentDetails?.plan?.plan_designs?.[0]?.plan_design_details?.[0]
            ?.benefit_period_ind,
          {
            optionsMap: benefitPeriodsMap,
            isRequired: true,
            infoText:
              '12 month period when accumulators begin and end for the year (e.g. Jan-Dec)',
          }
        ),
        defineFormField(
          'Plan Year/Renewal',
          'dropdownSelect',
          clientConfig.plan_year_renewal,
          currentDetails?.plan?.plan_year_renewal,
          {
            optionsMap: benefitPeriodsMap,
            infoText:
              '12 month period that defines open enrollment and plan elections. May differ from benefit period.',
            isRequired: true,
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Renewal Month',
          'dropdownSelect',
          clientConfig.renewal_month,
          currentDetails?.plan?.renewal_month,
          {
            optionsMap: monthsMap,
            infoText:
              'Month Account Management will renew client with RxBenefits. May differ from Plan Year/Renewal.',
            isRequired: true,
          }
        ),
        defineFormField(
          'Annual Reporting Start Month',
          'dropdownSelect',
          clientConfig.annual_reporting_start_month,
          currentDetails?.plan?.annual_reporting_start_month,
          {
            optionsMap: monthsMap,
            allowSearch: true,
            placeholder: 'Enter annual reporting start month',
            isRequired: true,
          }
        ),
      ]),
    ]),
    defineSubCategory('', '', [
      defineInlineFieldGroup([
        defineFormField(
          'Plan Class',
          'dropdownSelect',
          clientConfig.product_class_id,
          currentDetails?.plan?.product?.product_class_id,
          {
            optionsMap: planClassMap,
            isRequired: true,
            infoText: 'Most clients will be "self-funded"',
            placeholder: 'Enter plan class',
          }
        ),
        defineFormField(
          'ERISA Status',
          'dropdownSelect',
          clientConfig.erisa_ind,
          currentDetails?.plan?.erisa_ind,
          {
            optionsMap: erisaMap,
            isRequired: true,
            infoText:
              'ERISA is governing law for self-funded plans. Clients who are normally non-ERISA are schools or government agencies.',
            placeholder: 'Select an Option',
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Previous PBM',
          'input',
          clientConfig.previous_pbm,
          currentDetails?.plan?.previous_pbm,
          {
            placeholder: 'Enter previous PBM',
            validations: z
              .string()
              .max(100, 'Previous PBM cannot exceed 100 characters')
              .optional(),
          }
        ),
      ]),
    ]),
    defineSubCategory('', '', [
      defineInlineFieldGroup([
        defineFormField(
          'Primary Termination Reason',
          'dropdownSelect',
          clientConfig.primary_termination_reason,
          currentDetails?.organization?.primary_termination_reason,
          {
            placeholder: 'Primary Reason of Termination',
            optionsMap: terminationReasonMap,
          }
        ),
        defineFormField(
          'Secondary Termination Reason',
          'dropdownSelect',
          clientConfig.secondary_termination_reason,
          currentDetails?.organization?.secondary_termination_reason,
          {
            placeholder: 'Secondary Reason of Termination',
            optionsMap: terminationReasonMap,
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Termination Details',
          'input',
          clientConfig.termination_notes,
          currentDetails?.organization?.termination_notes,
          {
            isRequired: true,
            placeholder: 'Details of Termination',
            validations: z
              .string()
              .max(32768, 'Termination Details cannot exceed 32768 characters'),
          }
        ),
      ]),
    ]),
    defineSubCategory('', '', [
      defineInlineFieldGroup([
        defineFormField(
          'Is Client a 340B entity?',
          'dropdownSelect',
          clientConfig.is_340b_ind,
          currentDetails?.plan?.is_340b_ind,
          {
            isRequired: true,
            placeholder: 'Is Client a 340B entity?',
            optionsMap: yesNoMap,
          }
        ),
        defineFormField(
          'Plan Overview Note',
          'input',
          clientConfig.overview_notes,
          currentDetails?.plan?.overview_notes,
          {
            placeholder: 'Plan Overview Note',
            validations: z
              .string()
              .max(3000, 'Plan Overview Note cannot exceed 3000 characters'),
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Company Head Quarter State',
          'dropdownSelect',
          clientConfig.company_hq_state_ind,
          currentDetails?.organization?.company_hq_state_ind,
          {
            isRequired: true,
            infoText: 'If differs from Employer State',
            placeholder: 'Company Head Quarter State',
            optionsMap: stateMap,
          }
        ),
      ]),
    ]),
  ];

  return {
    subCategories,
  };
}
