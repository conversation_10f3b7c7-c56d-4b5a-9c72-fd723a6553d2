import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { useSaveChangeRequestHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/useOrganizationHooks';
import GenericForm from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/GenericForm';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import { CLAIMS_COVER_ITEM } from '../../../../Navigation/navigationConstants';
import { useEligibilityForm } from './eligibilityForm';

const EligibilityComponent: React.FC<{
  formMethods: UseFormReturn<any>;
  onUpdateActiveItem?: (id: string) => void;
}> = ({ formMethods, onUpdateActiveItem }) => {
  const { watch } = formMethods;
  const currentDetails = watch();
  const { subCategories } = useEligibilityForm(
    currentDetails as Partial<OrganizationDetails>,
    formMethods
  );

  const submitHandler = useSaveChangeRequestHandler(formMethods);

  const handleSaveAndExit = () => {
    // Submit the current form data
    const currentValues = formMethods.getValues();
    submitHandler(currentValues);
  };

  const handleBack = () => {
    if (onUpdateActiveItem) {
      onUpdateActiveItem('implementation');
    }
  };

  const handleContinue = () => {
    if (onUpdateActiveItem) {
      onUpdateActiveItem(CLAIMS_COVER_ITEM);
    }
  };

  return (
    <GenericForm
      formMethods={formMethods}
      formName="Eligibility"
      formDescription=""
      subCategories={subCategories}
      onContinue={handleContinue}
      onBack={handleBack}
      onSaveExit={handleSaveAndExit}
    />
  );
};

export default EligibilityComponent;
