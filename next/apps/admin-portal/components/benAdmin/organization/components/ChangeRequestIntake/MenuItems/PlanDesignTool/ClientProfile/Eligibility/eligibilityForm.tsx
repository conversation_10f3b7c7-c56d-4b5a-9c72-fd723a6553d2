import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { usePicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistMaps';
import {
  defineFormField,
  defineInlineFieldGroup,
  defineSubCategory,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';
import { SubCategoryType } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import { UseFormReturn } from 'react-hook-form';
import { z } from 'zod';

import { clientConfig } from '../../../../../Tabs/PlanDesign/ClientProfile/Config/clientConfig';

export const useEligibilityForm = (
  currentDetails: Partial<OrganizationDetails>,
  formMethods: UseFormReturn<OrganizationDetails>
) => {
  const { yesNoMap, studentAgeIndicatorMap } = usePicklistMaps();
  const planEligibility = currentDetails?.plan?.plan_eligibility;
  const planPDX = currentDetails?.plan?.plan_pdx;
  const planId = currentDetails?.plan?.plan_id;
  if (!planEligibility && planId) {
    formMethods?.setValue('plan.plan_eligibility.plan_id', planId);
  }
  const subCategories: SubCategoryType[] = [
    defineSubCategory('', '', [
      defineInlineFieldGroup([
        defineFormField(
          'COBRA',
          'dropdownSelect',
          clientConfig.cobra_ind,
          planEligibility?.cobra_ind,
          {
            isRequired: true,
            optionsMap: yesNoMap,
          }
        ),
        defineFormField(
          'Active Employees',
          'dropdownSelect',
          clientConfig.active_employees_ind,
          planEligibility?.active_employees_ind,
          {
            isRequired: true,
            optionsMap: yesNoMap,
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Retirees',
          'dropdownSelect',
          clientConfig.retirees_ind,
          planEligibility?.retirees_ind,
          {
            isRequired: true,
            optionsMap: yesNoMap,
          }
        ),
        defineFormField(
          'Retiree Subsidy (if yes)',
          'input',
          clientConfig.retiree_subsidy_ind,
          planEligibility?.retiree_subsidy_ind,
          {
            isRequired: true,
            placeholder: 'Enter Retiree Subsidy',
            validations: z
              .string()
              .max(1000, 'Retiree Subsidy cannot exceed 1000 characters'),
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Retiree Group Trust',
          'dropdownSelect',
          clientConfig.retirees_trust_ind,
          planPDX?.retirees_trust_ind,
          {
            isRequired: true,
            optionsMap: yesNoMap,
          }
        ),
      ]),
    ]),
    defineSubCategory('ESI Only', '', [
      defineInlineFieldGroup([
        defineFormField(
          'Dependent Age',
          'input',
          clientConfig.dependent_age,
          planEligibility?.dependent_age,
          {
            isRequired: true,
            placeholder: 'Enter Dependent Age',
            validations: z.number().int(),
          }
        ),
        defineFormField(
          'Dependent Age Indicator',
          'dropdownSelect',
          clientConfig.dependent_age_ind,
          planEligibility?.dependent_age_ind,
          {
            isRequired: true,
            optionsMap: studentAgeIndicatorMap,
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Student Age',
          'input',
          clientConfig.student_age,
          planEligibility?.student_age,
          {
            isRequired: true,
            placeholder: 'Enter Student Age',
            validations: z.number().int(),
          }
        ),
        defineFormField(
          'Student Age Indicator',
          'dropdownSelect',
          clientConfig.student_age_ind,
          planEligibility?.student_age_ind,
          {
            isRequired: true,
            optionsMap: studentAgeIndicatorMap,
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Dependent Alternate Groups',
          'dropdownSelect',
          clientConfig.dependent_alt_groups_ind,
          planEligibility?.dependent_alt_groups_ind,
          {
            isRequired: true,
            optionsMap: yesNoMap,
          }
        ),
      ]),
    ]),
  ];
  return { subCategories };
};
