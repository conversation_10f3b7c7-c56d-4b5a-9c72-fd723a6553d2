'use client';

import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { useSaveChangeRequestHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/useOrganizationHooks';
import GenericForm from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/GenericForm';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import { CLAIMS_COVER_ITEM } from '../../../../Navigation/navigationConstants';
import { useFsaHraHsaForm } from './fsaHraHsaForm';

interface FsaHraHsaComponentProps {
  formMethods: UseFormReturn<OrganizationDetails>;
  onUpdateActiveItem?: (id: string) => void;
}

const FsaHraHsaComponent: React.FC<FsaHraHsaComponentProps> = ({
  formMethods,
  onUpdateActiveItem,
}) => {
  const { watch } = formMethods;
  const currentDetails = watch();

  const { subCategories } = useFsaHraHsaForm(
    currentDetails as Partial<OrganizationDetails>
  );

  const submitHandler = useSaveChangeRequestHandler(formMethods);

  const handleSaveAndExit = () => {
    // Submit the current form data
    const currentValues = formMethods.getValues();
    submitHandler(currentValues);
  };

  const handleBack = () => {
    if (onUpdateActiveItem) {
      onUpdateActiveItem(CLAIMS_COVER_ITEM);
    }
  };

  const handleContinue = () => {
    if (onUpdateActiveItem) {
      onUpdateActiveItem('CLIENT_PROFILE_REVIEW');
    }
  };

  return (
    <GenericForm
      formName="FSA/HRA/HSA"
      formDescription=""
      formMethods={formMethods}
      subCategories={subCategories}
      onContinue={handleContinue}
      onBack={handleBack}
      onSaveExit={handleSaveAndExit}
    />
  );
};

export default FsaHraHsaComponent;
