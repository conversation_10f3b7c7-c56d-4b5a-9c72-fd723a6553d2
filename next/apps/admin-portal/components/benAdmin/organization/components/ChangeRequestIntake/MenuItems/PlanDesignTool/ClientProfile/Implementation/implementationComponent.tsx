import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { useSaveChangeRequestHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/useOrganizationHooks';
import GenericForm from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/GenericForm';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import { useImplementationForm } from './implementationForm';

const ImplementationComponent: React.FC<{
  formMethods: UseFormReturn<any>;
  onUpdateActiveItem?: (id: string) => void;
}> = ({ formMethods, onUpdateActiveItem }) => {
  const { watch } = formMethods;
  const currentDetails = watch();

  const { subCategories } = useImplementationForm(
    currentDetails as Partial<OrganizationDetails>
  );

  const submitHandler = useSaveChangeRequestHandler(formMethods);

  const handleSaveAndExit = () => {
    // Submit the current form data
    const currentValues = formMethods.getValues();
    submitHandler(currentValues);
  };

  const handleBack = () => {
    if (onUpdateActiveItem) {
      onUpdateActiveItem('in-house-pharmacy');
    }
  };

  const handleContinue = () => {
    if (onUpdateActiveItem) {
      onUpdateActiveItem('eligibility');
    }
  };

  return (
    <GenericForm
      formName="Implementation"
      formDescription=""
      formMethods={formMethods}
      subCategories={subCategories}
      onContinue={handleContinue}
      onBack={handleBack}
      onSaveExit={handleSaveAndExit}
    />
  );
};

export default ImplementationComponent;
