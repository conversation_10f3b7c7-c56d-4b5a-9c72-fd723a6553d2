import { AddIcon, ChevronDownIcon, DeleteIcon } from '@chakra-ui/icons';
import {
  Button,
  Flex,
  Menu,
  MenuButton,
  Menu<PERSON>tem,
  MenuList,
  ModalBody,
  ModalFooter,
} from '@chakra-ui/react';
import { Pharmacy } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { usePicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistMaps';
import {
  defineFormField,
  defineInlineFieldGroup,
  defineSubCategory,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';
import GenericForm from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/GenericForm';
import get from 'lodash/get';
import { FC, useCallback, useEffect, useMemo, useState } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { z } from 'zod';

import { getPharmacyBasepath } from '../../../../../Tabs/PlanDesign/ClientProfile/Config/pharmacyConfig';
import {} from '../../../../../Tabs/PlanDesign/ClientProfile/FieldData/maps';
import { currencyValidation, phoneValidation } from '../../validations';

interface InHousePharmacyFormProps {
  initialData?: Partial<Pharmacy>;
  onSave?: (data: any) => void;
  onCancel?: () => void;
  itemIndex?: number;
  isNewItem?: boolean;
  formMethods: UseFormReturn<any>;
}

export const InHousePharmacyForm: FC<InHousePharmacyFormProps> = ({
  initialData = {},
  onSave,
  onCancel,
  itemIndex = -1,
  isNewItem = true,
  formMethods,
}) => {
  const {
    pharmacyBillingMap,
    pharmacyOwnershipMap,
    pharmacyPricingMap,
    stateMap,
    pharmacyStatusMap,
    yesNoMap,
  } = usePicklistMaps();

  const basePath = getPharmacyBasepath(isNewItem, itemIndex);

  // Watch the form data at the base path
  const currentData = formMethods.watch(basePath) || initialData;

  // Initialize with legal_entity.addresses if available
  const initialAddresses = currentData?.legal_entity?.addresses?.length
    ? currentData.legal_entity.addresses.map((_: any, i: any) => i)
    : [0];
  const initialPhones = currentData?.legal_entity?.phones?.length
    ? currentData.legal_entity.phones.map((_: any, i: any) => i)
    : [0];

  // Set initial form values for addresses and phones if they exist
  useEffect(() => {
    if (currentData?.legal_entity?.addresses?.length) {
      currentData.legal_entity.addresses.forEach((address: any, index: any) => {
        if (address) {
          formMethods.setValue(
            `${basePath}.legal_entity.addresses[${index}].address_line_1`,
            address.address_line_1
          );
          formMethods.setValue(
            `${basePath}.legal_entity.addresses[${index}].address_line_2`,
            address.address_line_2
          );
          formMethods.setValue(
            `${basePath}.legal_entity.addresses[${index}].city`,
            address.city
          );
          formMethods.setValue(
            `${basePath}.legal_entity.addresses[${index}].state`,
            address.state
          );
          formMethods.setValue(
            `${basePath}.legal_entity.addresses[${index}].postal_code`,
            address.postal_code
          );
        }
      });
    }

    if (currentData?.legal_entity?.phones?.length) {
      currentData.legal_entity.phones.forEach((phone: any, index: any) => {
        if (phone) {
          formMethods.setValue(
            `${basePath}.legal_entity.phones[${index}].phone_number`,
            phone.phone_number
          );
        }
      });
    }
  }, [currentData, formMethods, basePath]);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(0);
  const [addresses, setAddresses] = useState(initialAddresses);
  const [phones, setPhones] = useState(initialPhones);

  const handleFormSubmit = useCallback(
    (formData: any) => {
      if (!onSave) return;

      // Get the submitted data from the form
      const submittedData = get(formData, basePath, {}) as any;

      //   // Process legal_entity data
      //   if (!submittedData.legal_entity) {
      //     submittedData.legal_entity = {
      //       name: '',
      //       legal_entity_id: 0,
      //       addresses: [],
      //       phones: [],
      //     };
      //   }

      // Process addresses
      const addressesData = [];
      for (let i = 0; i < addresses.length; i++) {
        const addressLine1 = formMethods.getValues(
          `${basePath}.legal_entity.addresses[${i}].address_line_1`
        );
        if (addressLine1) {
          addressesData.push({
            address_line_1: addressLine1,
            address_line_2:
              formMethods.getValues(
                `${basePath}.legal_entity.addresses[${i}].address_line_2`
              ) || '',
            city:
              formMethods.getValues(
                `${basePath}.legal_entity.addresses[${i}].city`
              ) || '',
            state:
              formMethods.getValues(
                `${basePath}.legal_entity.addresses[${i}].state`
              ) || '',
            postal_code:
              formMethods.getValues(
                `${basePath}.legal_entity.addresses[${i}].postal_code`
              ) || '',
            address_id: 0,
            owner_id: submittedData.legal_entity.legal_entity_id || 0,
            address_type: 'BUSINESS',
            owner_type: 'LEGAL_ENTITY',
            street: addressLine1,
            country_code: 'US',
            country: 'USA',
            created_date: new Date(),
            created_by: 0,
            updated_date: new Date(),
            updated_by: 0,
            version_number: 0,
          });
        }
      }
      submittedData.legal_entity.addresses = addressesData;

      // Process phones
      const phonesData = [];
      for (let i = 0; i < phones.length; i++) {
        const phoneNumber = formMethods.getValues(
          `${basePath}.legal_entity.phones[${i}].phone_number`
        );
        if (phoneNumber) {
          phonesData.push({
            phone_id: 0,
            owner_id: submittedData.legal_entity.legal_entity_id || 0,
            owner_type: 'LEGAL_ENTITY',
            phone_number: phoneNumber,
            phone_type: 'BUSINESS',
            is_primary: i === 0,
            created_date: new Date(),
            created_by: 0,
            updated_date: new Date(),
            updated_by: 0,
            version_number: 0,
          });
        }
      }
      submittedData.legal_entity.phones = phonesData;
      
      // No need to delete as we're not adding to pharmacy level anymore

      // For new items, merge with initial data
      const finalData = isNewItem
        ? { ...initialData, ...submittedData }
        : { ...currentData, ...submittedData };

      onSave(finalData);
    },
    [onSave, basePath, isNewItem, initialData, currentData]
  );

  const handleCancel = useCallback(() => {
    // Reset just this form's data to its original state
    if (isNewItem) {
      formMethods.setValue(basePath, initialData, {
        shouldValidate: false,
        shouldDirty: false,
        shouldTouch: false,
      });
    }
    onCancel?.();
  }, [formMethods, onCancel, basePath, initialData, isNewItem]);

  const formConfig = useMemo(() => {
    return {
      subCategories: [
        defineSubCategory('In-House Pharmacy General Information', '', [
          defineInlineFieldGroup([
            defineFormField(
              'In House Pharmacy Name',
              'input',
              `${basePath}.legal_entity.name`,
              currentData?.legal_entity?.name,
              {
                isRequired: true,
                validations: z
                  .string()
                  .max(255, 'Value cannot exceed 255 characters'),
              }
            ),
          ]),
          defineInlineFieldGroup([
            defineFormField(
              'In House Pharmacy Contact First Name',
              'input',
              `${basePath}.legal_entity.person_assignments[0].person.first_name`,
              currentData?.legal_entity?.person_assignments?.[0]?.person
                ?.first_name,
              {
                isRequired: true,
                validations: z
                  .string()
                  .max(50, 'Value cannot exceed 50 characters'),
              }
            ),
            defineFormField(
              'In House Pharmacy Contact Last Name',
              'input',
              `${basePath}.legal_entity.person_assignments[0].person.last_name`,
              currentData?.legal_entity?.person_assignments?.[0]?.person
                ?.last_name,
              {
                isRequired: true,
                validations: z
                  .string()
                  .max(50, 'Value cannot exceed 50 characters'),
              }
            ),
          ]),

          defineInlineFieldGroup([
            defineFormField(
              'In House Pharmacy Record',
              'input',
              `${basePath}.pharmacy_id`,
              currentData?.pharmacy_id,
              {
                validations: z.number().int().optional(),
              }
            ),
          ]),

          defineInlineFieldGroup([
            defineFormField(
              'In House Pharmacy Effective Date',
              'datepicker',
              `${basePath}.effective_date`,
              currentData?.effective_date,
              {
                isRequired: true,
                validations: z.date(),
              }
            ),
            defineFormField(
              'In House Pharmacy Expiration Date',
              'datepicker',
              `${basePath}.expiration_date`,
              currentData?.expiration_date,
              {
                validations: z.date().optional(),
              }
            ),
          ]),

          defineInlineFieldGroup([
            defineFormField(
              'In House Pharmacy Notes',
              'input',
              `${basePath}.notes`,
              currentData?.notes,
              {
                validations: z
                  .string()
                  .max(32768, 'Value cannot exceed 32768 characters')
                  .optional(),
              }
            ),
          ]),

          defineInlineFieldGroup([
            defineFormField(
              'In House Pharmacy Status',
              'dropdownSelect',
              `${basePath}.status_ind`,
              currentData?.status_ind,
              {
                optionsMap: pharmacyStatusMap,
                isRequired: true,
              }
            ),
          ]),
          defineInlineFieldGroup([
            defineFormField(
              'Pharmacy Ownership',
              'dropdownSelect',
              `${basePath}.pharmacy_ownership_ind`,
              currentData?.pharmacy_ownership_ind,
              {
                optionsMap: pharmacyOwnershipMap,
              }
            ),
          ]),

          defineInlineFieldGroup([
            defineFormField(
              'NPI Number',
              'input',
              `${basePath}.npi_number`,
              currentData?.npi_number,
              {
                isRequired: true,
                validations: z
                  .string()
                  .max(50, 'Value cannot exceed 50 characters'),
              }
            ),
          ]),

          defineInlineFieldGroup([
            defineFormField(
              'NABP Number',
              'input',
              `${basePath}.nabp_number`,
              currentData?.nabp_number,
              {
                isRequired: true,
                validations: z
                  .string()
                  .max(50, 'Value cannot exceed 50 characters'),
              }
            ),
          ]),
        ]),
        defineSubCategory('340b Administration', '', [
          defineInlineFieldGroup([
            defineFormField(
              '340b',
              'dropdownSelect',
              `${basePath}.is_340b`,
              currentData?.is_340b,
              {
                optionsMap: yesNoMap,
                isRequired: true,
              }
            ),
            defineFormField(
              '340b Admin Fee',
              'input',
              `${basePath}.admin_340b_fee`,
              currentData?.admin_340b_fee,
              {
                validations: currencyValidation.optional(),
              }
            ),
          ]),
        ]),
        defineSubCategory('In-House Pharmacy Setup', '', [
          defineInlineFieldGroup([
            defineFormField(
              'In House Pharmacy Billing',
              'dropdownSelect',
              `${basePath}.billing_ind`,
              currentData?.billing_ind,
              {
                infoText:
                  'Indicates whether client is invoiced for claims directly, or claims appear as a credit on the overall invoice',
                optionsMap: pharmacyBillingMap,
                isRequired: true,
              }
            ),
          ]),
          defineInlineFieldGroup([
            defineFormField(
              'Custom Rates Notes',
              'input',
              `${basePath}.custom_rate_notes`,
              currentData?.custom_rate_notes,
              {
                infoText:
                  'Additional details pertaining to custom rates for the client. IHP may be subject to different contract pricing than other delivery channels (e.g. retail, mail, etc.)',
                validations: z
                  .string()
                  .max(32768, 'Value cannot exceed 32768 characters')
                  .optional(),
              }
            ),
          ]),
          defineInlineFieldGroup([
            defineFormField(
              'Dispensing Fee Value',
              'input',
              `${basePath}.dispensing_fee_value`,
              currentData?.dispensing_fee_value,
              {
                validations: currencyValidation.optional(),
              }
            ),
            defineFormField(
              'Exclude from Financial Guarantee',
              'dropdownSelect',
              `${basePath}.financial_guarantee_ind`,
              currentData?.financial_guarantee_ind,
              {
                optionsMap: yesNoMap,
              }
            ),
          ]),
          defineInlineFieldGroup([
            defineFormField(
              'Exclude from Rebate Protection',
              'dropdownSelect',
              `${basePath}.rebate_protection_ind`,
              currentData?.rebate_protection_ind,
              {
                optionsMap: yesNoMap,
              }
            ),
            defineFormField(
              'In House Pharmacy Reimbursement Setup',
              'dropdownSelect',
              `${basePath}.pricing_setup_ind`,
              currentData?.pricing_setup_ind,
              {
                infoText:
                  'Details how the In-House Pharmacy is setup with the PBM for reimbursement.',
                optionsMap: pharmacyPricingMap,
              }
            ),
          ]),
          defineInlineFieldGroup([
            defineFormField(
              'Hardcoded Dispensing Fee',
              'dropdownSelect',
              `${basePath}.hardcoded_dispense_fee_ind`,
              currentData?.hardcoded_dispense_fee_ind,
              {
                optionsMap: yesNoMap,
              }
            ),
          ]),
          defineInlineFieldGroup([
            defineFormField(
              'Hardcoded Dispensing Fee Notes',
              'input',
              `${basePath}.hardcoded_dispense_fee_notes`,
              currentData?.hardcoded_dispense_fee_notes,
              {
                infoText:
                  'Additional details related to the hardcoded dispensing fee',
                validations: z
                  .string()
                  .max(32768, 'Value cannot exceed 32768 characters')
                  .optional(),
              }
            ),
          ]),
          defineInlineFieldGroup([
            defineFormField(
              'IHP Software',
              'input',
              `${basePath}.ihp_software`,
              currentData?.ihp_software,
              {
                validations: z
                  .string()
                  .max(255, 'Value cannot exceed 255 characters')
                  .optional(),
              }
            ),
            defineFormField(
              'IHP Switch',
              'input',
              `${basePath}.ihp_switch`,
              currentData?.ihp_switch,
              {
                validations: z
                  .string()
                  .max(255, 'Value cannot exceed 255 characters')
                  .optional(),
              }
            ),
          ]),

          defineInlineFieldGroup([
            defineFormField(
              'Multi-Ingredient Compounds Processed',
              'dropdownSelect',
              `${basePath}.multi_ingredient_compounds_ind`,
              currentData?.multi_ingredient_compounds_ind,
              {
                optionsMap: yesNoMap,
              }
            ),
          ]),
          defineInlineFieldGroup([
            defineFormField(
              'Pharmacy Name',
              'input',
              `${basePath}.name`,
              currentData?.name,
              {
                validations: z
                  .string()
                  .max(255, 'Value cannot exceed 255 characters')
                  .optional(),
              }
            ),
          ]),
        ]),
      ],
      // We'll keep this for backward compatibility but won't use it directly
      subCategoriesContact: [
        defineSubCategory(
          'In-House Pharmacy Contact Information',
          'Address 1',
          [
            defineInlineFieldGroup([
              defineFormField(
                'In House Pharmacy Address',
                'input',
                `${basePath}.address.address_line_1`,
                currentData?.address?.address_line_1,
                {
                  isRequired: true,
                  validations: z
                    .string()
                    .max(255, 'Address cannot exceed 255 characters'),
                }
              ),
            ]),
            defineInlineFieldGroup([
              defineFormField(
                'In House Pharmacy Address Line 2',
                'input',
                `${basePath}.address.address_line_2`,
                currentData?.address?.address_line_2,
                {
                  validations: z
                    .string()
                    .max(255, 'Address cannot exceed 255 characters')
                    .optional(),
                }
              ),
            ]),
            defineInlineFieldGroup([
              defineFormField(
                'In House Pharmacy City',
                'input',
                `${basePath}.address.city`,
                currentData?.address?.city,
                {
                  isRequired: true,
                  validations: z
                    .string()
                    .max(50, 'Value cannot exceed 50 characters'),
                }
              ),
              defineFormField(
                'In House Pharmacy State',
                'dropdownSelect',
                `${basePath}.address.state`,
                currentData?.address?.state,
                {
                  optionsMap: stateMap,
                  isRequired: true,
                }
              ),
              defineFormField(
                'In House Pharmacy Zip',
                'input',
                `${basePath}.address.postal_code`,
                currentData?.address?.postal_code,
                {
                  isRequired: true,
                  validations: z
                    .string()
                    .max(20, 'Value cannot exceed 20 characters'),
                }
              ),
            ]),
          ]
        ),
        defineSubCategory('In-House Pharmacy Phone 1', '', [
          defineInlineFieldGroup([
            defineFormField(
              'In House Pharmacy Phone',
              'input',
              `${basePath}.phone.phone_number`,
              currentData?.phone?.phone_number,
              {
                validations: phoneValidation.optional(),
              }
            ),
          ]),
        ]),
      ],
    };
  }, [
    currentData,
    basePath,
    pharmacyBillingMap,
    pharmacyOwnershipMap,
    pharmacyPricingMap,
    stateMap,
    pharmacyStatusMap,
    yesNoMap,
  ]);

  // Pagination state variables are already declared above

  // Calculate pagination for subcategories - exactly 2 pages
  const { totalPages, currentSubCategories } = useMemo(() => {
    return {
      totalPages: 2,
      currentSubCategories:
        currentPage === 0
          ? formConfig?.subCategories || []
          : generateDynamicContactSubCategories(),
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formConfig?.subCategories, currentPage, addresses, phones]);

  // Function to remove an address
  const removeAddress = (indexToRemove: number) => {
    setAddresses((prev: any[]) =>
      prev.filter((_: any, i: number) => i !== indexToRemove)
    );
  };

  // Function to remove a phone
  const removePhone = (indexToRemove: number) => {
    setPhones((prev: any[]) =>
      prev.filter((_: any, i: number) => i !== indexToRemove)
    );
  };

  // Generate dynamic address and phone subcategories
  function generateDynamicContactSubCategories() {
    const dynamicSubCategories = [];

    // Add a header for the contact information section
    dynamicSubCategories.push(
      defineSubCategory('In-House Pharmacy Contact Information', '', [])
    );

    // Add address subcategories
    addresses.forEach((_: any, index: number) => {
      dynamicSubCategories.push(
        defineSubCategory(`In House Pharmacy Address ${index + 1}`, '', [
          defineInlineFieldGroup([
            defineFormField(
              'In House Pharmacy Address',
              'input',
              `${basePath}.legal_entity.addresses[${index}].address_line_1`,
              currentData?.addresses?.[index]?.address_line_1 || '',
              {
                isRequired: index === 0,
                validations: z
                  .string()
                  .max(255, 'Address cannot exceed 255 characters'),
              }
            ),
          ]),
          defineInlineFieldGroup([
            defineFormField(
              'In House Pharmacy Address Line 2',
              'input',
              `${basePath}.legal_entity.addresses[${index}].address_line_2`,
              currentData?.addresses?.[index]?.address_line_2 || '',
              {
                validations: z
                  .string()
                  .max(255, 'Address cannot exceed 255 characters')
                  .optional(),
              }
            ),
          ]),
          defineInlineFieldGroup([
            defineFormField(
              'In House Pharmacy City',
              'input',
              `${basePath}.legal_entity.addresses[${index}].city`,
              currentData?.addresses?.[index]?.city || '',
              {
                isRequired: index === 0,
                validations: z
                  .string()
                  .max(50, 'Value cannot exceed 50 characters'),
              }
            ),
            defineFormField(
              'In House Pharmacy State',
              'dropdownSelect',
              `${basePath}.legal_entity.addresses[${index}].state`,
              currentData?.addresses?.[index]?.state || '',
              {
                optionsMap: stateMap,
                isRequired: index === 0,
              }
            ),
            defineFormField(
              'In House Pharmacy Zip',
              'input',
              `${basePath}.legal_entity.addresses[${index}].postal_code`,
              currentData?.addresses?.[index]?.postal_code || '',
              {
                isRequired: index === 0,
                validations: z
                  .string()
                  .max(20, 'Value cannot exceed 20 characters'),
              }
            ),
          ]),
        ])
      );
    });

    // Add phone subcategories
    phones.forEach((_: any, index: number) => {
      dynamicSubCategories.push(
        defineSubCategory(`In House Pharmacy Phone ${index + 1}`, '', [
          defineInlineFieldGroup([
            defineFormField(
              'In House Pharmacy Phone',
              'input',
              `${basePath}.legal_entity.phones[${index}].phone_number`,
              currentData?.phones?.[index]?.phone_number || '',
              {
                validations: phoneValidation.optional(),
              }
            ),
          ]),
        ])
      );
    });

    return dynamicSubCategories;
  }

  // Navigation handlers
  const handleNavigation = useCallback(
    (direction: 'next' | 'back') => {
      if (direction === 'next') {
        if (currentPage < totalPages - 1) {
          setCurrentPage((prev) => prev + 1);
        } else {
          formMethods.handleSubmit(handleFormSubmit)();
        }
      } else {
        if (currentPage > 0) {
          setCurrentPage((prev) => prev - 1);
        } else {
          handleCancel();
        }
      }
    },
    [currentPage, totalPages, formMethods, handleFormSubmit, handleCancel]
  );

  // Button text logic
  const buttonText = useMemo(() => {
    return currentPage === 0 ? 'Next' : 'Save';
  }, [currentPage]);

  // Render pagination component
  const renderPagination = () => (
    <Menu>
      <MenuButton
        as={Button}
        rightIcon={<ChevronDownIcon />}
        size="sm"
        variant="outline"
        colorScheme="gray"
      >
        Page {currentPage + 1} of {totalPages}
      </MenuButton>
      <MenuList>
        <MenuItem
          onClick={() => setCurrentPage(0)}
          bg={currentPage === 0 ? 'gray.50' : 'transparent'}
        >
          {currentPage === 0 && (
            <ChevronDownIcon transform="rotate(-90deg)" color="gray.500" />
          )}
          Page 1
        </MenuItem>
        <MenuItem
          onClick={() => setCurrentPage(1)}
          bg={currentPage === 1 ? 'gray.50' : 'transparent'}
        >
          {currentPage === 1 && (
            <ChevronDownIcon transform="rotate(-90deg)" color="gray.500" />
          )}
          Page 2
        </MenuItem>
      </MenuList>
    </Menu>
  );

  return (
    <>
      <ModalBody>
        <GenericForm
          formMethods={formMethods}
          formName="In-House Pharmacy Form"
          formDescription=""
          subCategories={currentSubCategories}
          showButtons={false}
          isInModal
        />

        {currentPage === 1 && (
          <Flex direction="column" mt={4} gap={2}>
            <Flex justify="flex-end" width="100%">
              <Button
                leftIcon={<AddIcon />}
                size="sm"
                onClick={() =>
                  setAddresses((prev: string | any[]) => [...prev, prev.length])
                }
                variant="outline"
                colorScheme="green"
              >
                Add Another Address
              </Button>
            </Flex>

            {addresses.length > 1 && (
              <Flex justify="flex-end" width="100%" mt={2}>
                <Flex gap={2} wrap="wrap" justify="flex-end">
                  {addresses.slice(1).map((_: any, i: number) => (
                    <Button
                      key={i + 1}
                      leftIcon={<DeleteIcon />}
                      size="sm"
                      colorScheme="green"
                      variant="outline"
                      onClick={() => removeAddress(i + 1)}
                    >
                      Delete Address {i + 2}
                    </Button>
                  ))}
                </Flex>
              </Flex>
            )}

            <Flex justify="flex-end" width="100%" mt={4}>
              <Button
                leftIcon={<AddIcon />}
                size="sm"
                onClick={() =>
                  setPhones((prev: string | any[]) => [...prev, prev.length])
                }
                variant="outline"
                colorScheme="green"
              >
                Add Another Phone
              </Button>
            </Flex>

            {phones.length > 1 && (
              <Flex justify="flex-end" width="100%" mt={2}>
                <Flex gap={2} wrap="wrap" justify="flex-end">
                  {phones.slice(1).map((_: any, i: number) => (
                    <Button
                      key={i + 1}
                      leftIcon={<DeleteIcon />}
                      size="sm"
                      colorScheme="green"
                      variant="outline"
                      onClick={() => removePhone(i + 1)}
                    >
                      Delete Phone {i + 2}
                    </Button>
                  ))}
                </Flex>
              </Flex>
            )}
          </Flex>
        )}
      </ModalBody>
      <ModalFooter>
        <Flex width="100%" justify="space-between" align="center">
          {renderPagination()}
          <Flex gap={3} ml="auto">
            <Button variant="outline" onClick={() => handleNavigation('back')}>
              {currentPage === 0 ? 'Cancel' : 'Previous'}
            </Button>
            <Button
              colorScheme="green"
              onClick={() => handleNavigation('next')}
            >
              {buttonText}
            </Button>
          </Flex>
        </Flex>
      </ModalFooter>
    </>
  );
};
