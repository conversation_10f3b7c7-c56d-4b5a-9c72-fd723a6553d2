import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { useSaveChangeRequestHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/useOrganizationHooks';
import GenericForm from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/GenericForm';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import { usePharmacyBenefitsManager } from './pharmacyBenefitsManagerForm';

const PharmacyBenefitsManagerComponent: React.FC<{
  formMethods: UseFormReturn<OrganizationDetails>;
  onUpdateActiveItem?: (id: string) => void;
}> = ({ formMethods, onUpdateActiveItem }) => {
  const { watch } = formMethods;
  const currentDetails = watch();

  const { subCategories } = usePharmacyBenefitsManager(
    currentDetails as Partial<OrganizationDetails>
  );

  const submitHandler = useSaveChangeRequestHandler(formMethods);

  const handleSaveAndExit = () => {
    // Submit the current form data
    const currentValues = formMethods.getValues();
    submitHandler(currentValues);
  };

  const handleContinue = () => {
    // Seamlessly update the active menu item to "pharmacy-benefits-manager" via the callback.
    if (onUpdateActiveItem) {
      onUpdateActiveItem('in-house-pharmacy');
    }
  };

  const handleBack = () => {
    if (onUpdateActiveItem) {
      onUpdateActiveItem('client-information');
    }
  };

  return (
    <GenericForm
      formDescription=""
      formName="Pharmacy Benefits Manager"
      formMethods={formMethods}
      subCategories={subCategories}
      onContinue={handleContinue}
      onBack={handleBack}
      onSaveExit={handleSaveAndExit}
    />
  );
};

export default PharmacyBenefitsManagerComponent;
