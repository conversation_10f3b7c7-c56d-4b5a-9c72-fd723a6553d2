// ClientProfileReview.tsx
import {
  FSA_HRA_HSA_ITEM,
  OrganizationDetails,
} from 'apps/admin-portal/components/benAdmin';
import { useSaveChangeRequestHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/useOrganizationHooks';
import FormReviewSummary from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/FormReviewSummary';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import { useClientProfileSections } from './clientProfileSections';

interface ReviewProps {
  formMethods: UseFormReturn<any>;
  onUpdateActiveItem?: (id: string) => void;
  currentDetails?: Partial<OrganizationDetails>;
}

const ClientProfileReview: React.FC<ReviewProps> = ({
  formMethods,
  onUpdateActiveItem,
  currentDetails,
}) => {
  const saveHandler = useSaveChangeRequestHandler(formMethods);
  const { watch } = formMethods;

  // Use the current details from the form if not explicitly provided
  const details = currentDetails || watch();

  // Get the sections for the review
  const formSections = useClientProfileSections(details);

  const handleNavigation = (sectionId?: string) => {
    if (onUpdateActiveItem) {
      onUpdateActiveItem(sectionId || FSA_HRA_HSA_ITEM);
    }
  };

  const handleSaveExit = () => {
    const formData = formMethods.getValues();
    saveHandler(formData);
  };

  return (
    <FormReviewSummary
      title="Review and Save Changes"
      description=""
      subtitle="Client Profile"
      formMethods={formMethods}
      formSections={formSections}
      onBack={handleNavigation}
      onSaveExit={handleSaveExit}
    />
  );
};

export default ClientProfileReview;
