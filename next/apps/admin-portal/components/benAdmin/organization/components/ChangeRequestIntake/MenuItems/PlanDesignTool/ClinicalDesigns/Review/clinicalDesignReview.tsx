import {
  CLINICAL_DESIGN_MODE,
  OrganizationDetails,
  useSaveChangeRequestHandler,
} from 'apps/admin-portal/components/benAdmin';
import FormReviewSummary from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/FormReviewSummary';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import { useClinicalDesignsSections } from './clinicalDesignSections';

interface ReviewClinicalDesignsProps {
  formMethods: UseFormReturn<OrganizationDetails>;
  onUpdateActiveItem?: (id: string) => void;
}

const ReviewClinicalDesigns: React.FC<ReviewClinicalDesignsProps> = ({
  formMethods,
  onUpdateActiveItem,
}) => {
  const saveHandler = useSaveChangeRequestHandler(formMethods);
  const { watch } = formMethods;
  const currentDetails = watch();

  // Use our dynamic sections hook
  const formSections = useClinicalDesignsSections(
    currentDetails as Partial<OrganizationDetails>
  );

  // Handle navigation to a specific section
  const handleNavigation = (sectionId?: string) => {
    if (onUpdateActiveItem) {
      if (sectionId) {
        onUpdateActiveItem(sectionId);
      } else {
        onUpdateActiveItem(
          formSections.sections.at(-1)?.id || CLINICAL_DESIGN_MODE
        );
      }
    }
  };

  // Save and exit handler
  const handleSaveExit = () => {
    const formData = formMethods.getValues();
    saveHandler(formData);
  };

  return (
    <FormReviewSummary
      title="Review and Save Changes"
      description=""
      subtitle="Clinical Designs"
      formMethods={formMethods}
      formSections={formSections}
      onBack={handleNavigation}
      onSaveExit={handleSaveExit}
    />
  );
};

export default ReviewClinicalDesigns;
