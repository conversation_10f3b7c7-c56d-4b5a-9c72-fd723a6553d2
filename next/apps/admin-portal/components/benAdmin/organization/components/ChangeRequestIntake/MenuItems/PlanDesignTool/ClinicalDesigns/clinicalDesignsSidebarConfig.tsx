// ... existing imports
import {
  buildGroupedSidebarItems,
  CLINICAL_DESIGN_MODE,
  OrganizationDetails,
  SidebarConfig,
} from 'apps/admin-portal/components/benAdmin';
import { UseFormReturn } from 'react-hook-form';

import ReviewClinicalDesigns from './Review/clinicalDesignReview';

export const getDynamicClinicalDesignConfig = (
  changeRequest: OrganizationDetails,
  formMethods: UseFormReturn<any>,
  navigateFn?: (section: string, tab: string) => void
): SidebarConfig => {
  const navigateToClinicalDesignItem = (id: string) =>
    navigateFn ? navigateFn(CLINICAL_DESIGN_MODE, id) : undefined;

  // Early return if no features available
  if (
    !changeRequest?.plan?.plan_designs[0].plan_features ||
    !Array.isArray(changeRequest?.plan?.plan_designs[0].plan_features)
  ) {
    return { sections: [] };
  }

  // Build items with auto-grouping for similar feature names
  // Exclude Pharmacy Network
  const items = buildGroupedSidebarItems(
    changeRequest.features,
    formMethods,
    navigateToClinicalDesignItem,
    undefined, // No specific includes
    ['Pharmacy Network'] // Exclude Pharmacy Network
  );

  // Add review section at the end
  items.push({
    id: 'CLINICAL_DESIGN_REVIEW',
    label: 'Review & Save',
    component: (
      <ReviewClinicalDesigns
        formMethods={formMethods}
        onUpdateActiveItem={navigateToClinicalDesignItem}
      />
    ),
  });

  return {
    sections: [
      {
        title: 'CLINICAL DESIGN',
        items,
      },
    ],
  };
};
