'use client';

import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { useSaveChangeRequestHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/useOrganizationHooks';
import GenericForm from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/GenericForm';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import { useMemberServicesForm } from './memberServicesForm';

interface MemberServicesComponentProps {
  formMethods: UseFormReturn<OrganizationDetails>;
  onUpdateActiveItem?: (id: string) => void;
}

const MemberServicesComponent: React.FC<MemberServicesComponentProps> = ({
  formMethods,
  onUpdateActiveItem,
}) => {
  const { getValues, setValue } = formMethods;
  const currentDetails = getValues();

  const { subCategories } = useMemberServicesForm(
    currentDetails as OrganizationDetails,
    setValue
  );

  const submitHandler = useSaveChangeRequestHandler(formMethods);

  const handleSaveAndExit = () => {
    // Submit the current form data
    const currentValues = formMethods.getValues();
    submitHandler(currentValues);
  };

  const handleBack = () => {
    if (onUpdateActiveItem) {
      onUpdateActiveItem('welcome-kit-and-letters');
    }
  };

  const handleContinue = () => {
    if (onUpdateActiveItem) {
      onUpdateActiveItem('member-services-review');
    }
  };

  return (
    <GenericForm
      formName="Member Services Providers"
      formDescription=""
      formMethods={formMethods}
      subCategories={subCategories}
      onContinue={handleContinue}
      onBack={handleBack}
      onSaveExit={handleSaveAndExit}
    />
  );
};

export default MemberServicesComponent;
