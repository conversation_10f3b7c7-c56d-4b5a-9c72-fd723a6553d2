import {
  LegalEntityAssignments,
  OrganizationDetails,
} from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import {
  defineForm<PERSON>ield,
  defineInlineFieldGroup,
  defineSubCategory,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';
import {
  NullableLegalEntitys,
  SubCategoryType,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import set from 'lodash/set';
import { useEffect, useRef } from 'react';
import { UseFormSetValue } from 'react-hook-form';
import { z } from 'zod';

import {
  ENTITY_ROLES,
  EntityAssignments,
  extractPlanDesignData,
  getEmptyLegalEntityData,
  getEntityPath,
} from '../../../../../Tabs/PlanDesign/MemberExperience/Config/memberServicesConfig';
import { phoneValidation } from '../../validations';

const nameValidation = z.string().max(50, 'Name cannot exceed 50 characters');

export const useMemberServicesForm = (
  currentDetails: Partial<OrganizationDetails>,
  setValue: UseFormSetValue<OrganizationDetails>
) => {
  // Use a ref to track initialization
  const isInitialized = useRef(false);

  // Move this logic into useEffect
  useEffect(() => {
    // Skip if already initialized or if we don't have current details
    if (isInitialized.current || !currentDetails) return;

    let memberServiceFormData: EntityAssignments[] = [];
    let updatedEntityAssignmentData: NullableLegalEntitys[] = [];
    const entityData: Partial<LegalEntityAssignments[]> = currentDetails?.plan
      ?.legal_entity_assignment?.length
      ? currentDetails?.plan?.legal_entity_assignment
      : [];

    if (currentDetails?.plan?.legal_entity_assignment?.length === 0) {
      updatedEntityAssignmentData = getEmptyLegalEntityData(
        null,
        [
          ENTITY_ROLES.paProvider,
          ENTITY_ROLES.memberServices,
          ENTITY_ROLES.medicalVendor,
        ],
        4
      );
    } else if (memberServiceFormData?.length === 0) {
      memberServiceFormData = extractPlanDesignData(currentDetails);
      for (let i = 0; i < memberServiceFormData?.length; i++) {
        if (!memberServiceFormData[i]?.paAvailability)
          updatedEntityAssignmentData = [
            ...updatedEntityAssignmentData,
            ...getEmptyLegalEntityData(
              memberServiceFormData[i]?.planDesignId,
              [ENTITY_ROLES.paProvider],
              0
            ),
          ];
        if (!memberServiceFormData[i]?.msAvailability)
          updatedEntityAssignmentData = [
            ...updatedEntityAssignmentData,
            ...getEmptyLegalEntityData(
              memberServiceFormData[i]?.planDesignId,
              [ENTITY_ROLES.memberServices],
              0
            ),
          ];
        if (!memberServiceFormData[i]?.medAvailability)
          updatedEntityAssignmentData = [
            ...updatedEntityAssignmentData,
            ...getEmptyLegalEntityData(
              memberServiceFormData[i]?.planDesignId,
              [ENTITY_ROLES.medicalVendor],
              0
            ),
          ];
        if (memberServiceFormData[i]?.tpCount !== 3)
          updatedEntityAssignmentData = [
            ...updatedEntityAssignmentData,
            ...getEmptyLegalEntityData(
              memberServiceFormData[i]?.planDesignId,
              [],
              4 - (memberServiceFormData[i]?.tpCount + 1)
            ),
          ];
      }
    }

    // Only update if we have entity data to update
    if (entityData.length > 0 || updatedEntityAssignmentData.length > 0) {
      const newValues = JSON.parse(JSON.stringify(currentDetails));
      const newL = [...entityData, ...updatedEntityAssignmentData];
      set(newValues, 'plan.legal_entity_assignment', newL);

      // Batch the setValue calls to reduce renders
      Object.keys(newValues).forEach((key: any) => {
        setValue(key, newValues[key]);
      });

      // Mark as initialized to prevent future runs
      isInitialized.current = true;
    }
  }, [currentDetails, setValue]);

  // Define form data
  const memberServiceFormData: EntityAssignments[] = currentDetails?.plan
    ?.legal_entity_assignment?.length
    ? extractPlanDesignData(currentDetails)
    : [];

  const entityData: Partial<LegalEntityAssignments[]> = currentDetails?.plan
    ?.legal_entity_assignment?.length
    ? currentDetails?.plan?.legal_entity_assignment
    : [];

  const subCategories: SubCategoryType[] = memberServiceFormData?.flatMap(
    (data: Partial<EntityAssignments>) => {
      return defineSubCategory('', '', [
        defineInlineFieldGroup([
          defineFormField(
            'General Member Service Vendor',
            'input',
            getEntityPath(entityData, ENTITY_ROLES.memberServices, 'name'),
            data?.memberService?.name,
            {
              placeholder: 'Enter General Member Service Vendor',
              infoText: 'Who members call for Rx questions.',
              validations: nameValidation.optional(),
            }
          ),
          defineFormField(
            'General Member Service Phone Number',
            'input',
            getEntityPath(entityData, ENTITY_ROLES.memberServices, 'phone'),
            data?.memberService?.phone,
            {
              placeholder: 'Enter General Member Service Phone',
              validations: phoneValidation.optional(),
            }
          ),
        ]),
        defineInlineFieldGroup([
          defineFormField(
            'Third Party Service 1',
            'input',
            getEntityPath(
              entityData,
              ENTITY_ROLES.thirdPartyServices,
              'name',
              0
            ),
            data?.thirdPartyServices?.[0]?.name,
            {
              placeholder: 'Enter Third Party Service 1',
              infoText:
                'Additional vendor(s) with whom the client has a relationship.',
              validations: nameValidation.optional(),
            }
          ),
          defineFormField(
            'Third Party Service 1 Phone Number',
            'input',
            getEntityPath(
              entityData,
              ENTITY_ROLES.thirdPartyServices,
              'phone',
              0
            ),
            data?.thirdPartyServices?.[0]?.phone,
            {
              placeholder: 'Enter Third Party Service 1 Phone',
              validations: phoneValidation.optional(),
            }
          ),
        ]),
        defineInlineFieldGroup([
          defineFormField(
            'Third Party Service 2',
            'input',
            getEntityPath(
              entityData,
              ENTITY_ROLES.thirdPartyServices,
              'name',
              1
            ),
            data?.thirdPartyServices?.[1]?.name,
            {
              placeholder: 'Enter Third Party Service 2',
              infoText:
                'Additional vendor(s) with whom the client has a relationship.',
              validations: nameValidation.optional(),
            }
          ),
          defineFormField(
            'Third Party Service 2 Phone Number',
            'input',
            getEntityPath(
              entityData,
              ENTITY_ROLES.thirdPartyServices,
              'phone',
              1
            ),
            data?.thirdPartyServices?.[1]?.phone,
            {
              placeholder: 'Enter Third Party Service 2 Phone',
              validations: phoneValidation.optional(),
            }
          ),
        ]),
        defineInlineFieldGroup([
          defineFormField(
            'Third Party Service 3',
            'input',
            getEntityPath(
              entityData,
              ENTITY_ROLES.thirdPartyServices,
              'name',
              2
            ),
            data?.thirdPartyServices?.[2]?.name,
            {
              placeholder: 'Enter Third Party Service 3',
              infoText:
                'Additional vendor(s) with whom the client has a relationship',
              validations: nameValidation.optional(),
            }
          ),
          defineFormField(
            'Third Party Service 3 Phone Number',
            'input',
            getEntityPath(
              entityData,
              ENTITY_ROLES.thirdPartyServices,
              'phone',
              2
            ),
            data?.thirdPartyServices?.[2]?.phone,
            {
              placeholder: 'Enter Third Party Service 3 Phone',
              validations: phoneValidation.optional(),
            }
          ),
        ]),
        defineInlineFieldGroup([
          defineFormField(
            'Third Party Service 4',
            'input',
            getEntityPath(
              entityData,
              ENTITY_ROLES.thirdPartyServices,
              'name',
              3
            ),
            data?.thirdPartyServices?.[3]?.name,
            {
              placeholder: 'Enter Third Party Service 4',
              infoText:
                'Additional vendor(s) with whom the client has a relationship.',
              validations: nameValidation.optional(),
            }
          ),
          defineFormField(
            'Third Party Service 4 Phone Number',
            'input',
            getEntityPath(
              entityData,
              ENTITY_ROLES.thirdPartyServices,
              'phone',
              3
            ),
            data?.thirdPartyServices?.[3]?.phone,
            {
              placeholder: 'Enter Third Party Service 4 Phone',
              validations: phoneValidation.optional(),
            }
          ),
        ]),
      ]);
    }
  );

  return { subCategories };
};
