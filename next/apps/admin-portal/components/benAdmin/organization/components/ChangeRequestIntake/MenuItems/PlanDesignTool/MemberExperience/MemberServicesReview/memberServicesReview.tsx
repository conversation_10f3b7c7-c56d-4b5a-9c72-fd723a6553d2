// ReviewMemberServices.tsx
import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { useSaveChangeRequestHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/useOrganizationHooks';
import FormReviewSummary from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/FormReviewSummary';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import { useMemberServicesSections } from './memberServicesSection';

interface ReviewProps {
  formMethods: UseFormReturn<OrganizationDetails>;
  onUpdateActiveItem?: (id: string) => void;
}

const ReviewMemberServices: React.FC<ReviewProps> = ({
  formMethods,
  onUpdateActiveItem,
}) => {
  const saveHandler = useSaveChangeRequestHandler(formMethods);
  const { watch, setValue } = formMethods;
  const currentDetails = watch();
  const formSections = useMemberServicesSections(
    currentDetails as Partial<OrganizationDetails>,
    setValue
  );

  const handleNavigation = (sectionId?: string) => {
    if (onUpdateActiveItem) {
      onUpdateActiveItem(sectionId || 'member-services');
    }
  };

  const handleSaveExit = () => {
    const formData = formMethods.getValues();
    saveHandler(formData);
  };

  return (
    <FormReviewSummary
      title="Review and Save Changes"
      description=""
      subtitle="Member Experience"
      formMethods={formMethods}
      formSections={formSections}
      onBack={handleNavigation}
      onSaveExit={handleSaveExit}
    />
  );
};

export default ReviewMemberServices;
