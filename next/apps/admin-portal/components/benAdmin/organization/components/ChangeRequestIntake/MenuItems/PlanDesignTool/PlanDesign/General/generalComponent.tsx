import { useSaveChangeRequestHandler } from 'apps/admin-portal/components/benAdmin';
import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { createPlanDesignSubmitHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/PlanDesign/planSubmitHandlers';
import GenericForm from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/GenericForm';
import React, { useCallback } from 'react';
import { UseFormReturn } from 'react-hook-form';

import {
  PLAN_DESIGN_LIST_ITEM,
  XML_ITEM,
} from '../../../../Navigation/navigationConstants';
import { useGeneralForm } from './generalForm';

interface ClientInformationComponentProps {
  formMethods: UseFormReturn<any>;
  onUpdateActiveItem?: (id: string) => void;
}

const GeneralComponent: React.FC<ClientInformationComponentProps> = ({
  formMethods,
  onUpdateActiveItem,
}) => {
  const { watch, getValues } = formMethods;
  const currentDetails = watch();

  const baseSaveHandler = useSaveChangeRequestHandler(formMethods);

  // Create an enhanced submit handler that handles multi-plan updates
  const submitHandler = createPlanDesignSubmitHandler(baseSaveHandler);

  // Handle save and exit action
  const handleSaveAndExit = useCallback(() => {
    const currentValues = getValues();
    submitHandler(currentValues);
  }, [getValues, submitHandler]);
  // Build form subCategories and get the original continue/back handlers.
  const { subCategories } = useGeneralForm(
    currentDetails as Partial<OrganizationDetails>
  );

  // When the user clicks "Continue", run any internal continue logic then update the active menu item.
  const handleContinue = () => {
    if (onUpdateActiveItem) {
      onUpdateActiveItem(XML_ITEM);
    }
  };

  const handleBack = () => {
    if (onUpdateActiveItem) {
      onUpdateActiveItem(PLAN_DESIGN_LIST_ITEM);
    }
  };

  return (
    <GenericForm
      formMethods={formMethods}
      formName="General"
      formDescription=""
      subCategories={subCategories}
      onContinue={handleContinue}
      onBack={handleBack}
      onSaveExit={handleSaveAndExit}
    />
  );
};

export default GeneralComponent;
