import {
  DISPENSE_ITEM,
  PLAN_DESIGNS_BASE_PATH,
  UNBREAKABLE_ITEM,
  useSaveChangeRequestHandler,
} from 'apps/admin-portal/components/benAdmin';
import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { createPlanDesignSubmitHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/PlanDesign/planSubmitHandlers';
import GenericForm from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/GenericForm';
import { getIndexFromURL } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/SideNavBar/parameterUtils';
import { useSearchParams } from 'next/navigation';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { UseFormReturn } from 'react-hook-form';

import { useCompoundForm } from './compoundForm';

interface CompoundComponentProps {
  formMethods: UseFormReturn<any>;
  onUpdateActiveItem?: (id: string) => void;
}

/**
 * CompoundComponent
 *
 * Handles the compound coverage configuration for a plan design.
 * Allows users to specify if compounds are covered and select which
 * patient pay tier applies to compounds.
 */
const CompoundComponent: React.FC<CompoundComponentProps> = ({
  formMethods,
  onUpdateActiveItem,
}) => {
  const { watch, getValues, setValue } = formMethods;
  const currentDetails = watch();
  const searchParams = useSearchParams();
  const emptyState = searchParams?.get('emptyState') === 'true';

  const urlIndex = getIndexFromURL();
  const indexToUse = urlIndex !== undefined ? urlIndex : 0;
  const planDesignsDestails = `${PLAN_DESIGNS_BASE_PATH}.${indexToUse}.plan_design_details.0`;
  const basePath = `${planDesignsDestails}.cost_share_design`;

  // Get initial compounds_ind value for disabled state
  const initialCompoundsInd =
    currentDetails.plan?.plan_designs?.[indexToUse]?.plan_design_details?.[0]
      ?.compounds_ind;

  const [isCompoundSelectDisabled, setIsCompoundSelectDisabled] = useState(
    initialCompoundsInd !== '1' && initialCompoundsInd !== 1
  );

  const baseSaveHandler = useSaveChangeRequestHandler(formMethods);
  const submitHandler = createPlanDesignSubmitHandler(baseSaveHandler);

  // Create options map from cost share designs
  const costShareDesignOptions = useMemo(() => {
    const designs = getValues(basePath) || [];

    if (!Array.isArray(designs)) {
      return {};
    }

    return designs.reduce(
      (acc: Record<string, string>, design: any, index: number) => {
        if (design?.name) {
          if (emptyState) {
            // In empty state, only show standard designs
            if (design.pre_packaged_ind === 0) {
              acc[index.toString()] = design.name;
            }
          } else {
            // Show all with S/U indicator
            acc[index.toString()] = `${design.name} ${
              design.pre_packaged_ind === 0 ? '(S)' : '(U)'
            }`;
          }
        }
        return acc;
      },
      {}
    );
  }, [getValues, basePath, emptyState]);

  // Handle compounds covered change
  const handleCompoundsCoveredChange = useCallback(
    (value: string) => {
      const isDisabled = value !== '1';
      setIsCompoundSelectDisabled(isDisabled);

      if (isDisabled) {
        // Reset the selected compound design to null
        const selectionPath = `${PLAN_DESIGNS_BASE_PATH}.${indexToUse}.plan_design_details.0.selected_compound_design`;
        setValue(selectionPath, null, {
          shouldDirty: true,
          shouldTouch: true,
          shouldValidate: true,
        });

        // Reset all compounds_ind values to 0
        const designs = getValues(basePath) || [];
        if (Array.isArray(designs)) {
          designs.forEach((_, index) => {
            const path = `${planDesignsDestails}.compounds_ind`;
            setValue(path, 0, { shouldDirty: true });
          });
          setValue(basePath, designs, { shouldDirty: true });
        }
      }
    },
    [indexToUse, setValue, getValues, basePath, planDesignsDestails]
  );

  // Handle cost share design selection
  const handleCostShareDesignChange = useCallback(
    (selectedIndex: string) => {
      const designs = getValues(basePath) || [];

      if (!Array.isArray(designs)) {
        return;
      }

      // Reset all compounds_ind values to 0
      designs.forEach((_, index) => {
        const path = `${planDesignsDestails}.compounds_ind`;
        setValue(path, 0, { shouldDirty: true });
      });

      // Set selected design's compounds_ind to 1
      const selectedPath = `${planDesignsDestails}.compounds_ind`;
      setValue(selectedPath, 1, { shouldDirty: true });

      setValue(basePath, designs, { shouldDirty: true });
    },
    [getValues, basePath, setValue, planDesignsDestails]
  );

  const handleSaveAndExit = useCallback(() => {
    submitHandler(getValues());
  }, [submitHandler, getValues]);

  // Add watcher for plan design details
  useEffect(() => {
    const subscription = watch((value, { name, type }) => {
      const planDesignDetails =
        value.plan?.plan_designs?.[indexToUse]?.plan_design_details?.[0];
      console.log('Plan Design Details Changed:', {
        name,
        type,
        planDesignDetails,
        compoundsInd: planDesignDetails?.compounds_ind,
        selectedCompoundDesign: planDesignDetails?.selected_compound_design,
        costShareDesign: planDesignDetails?.cost_share_design,
      });
    });
    return () => subscription.unsubscribe();
  }, [watch, indexToUse]);

  const handleContinue = () => {
    onUpdateActiveItem?.(DISPENSE_ITEM);
  };

  const handleBack = () => {
    onUpdateActiveItem?.(UNBREAKABLE_ITEM);
  };

  // Build form subcategories
  const { subCategories } = useCompoundForm(
    currentDetails as Partial<OrganizationDetails>,
    costShareDesignOptions,
    handleCostShareDesignChange,
    indexToUse,
    isCompoundSelectDisabled,
    handleCompoundsCoveredChange
  );

  return (
    <GenericForm
      formMethods={formMethods}
      formName="Compounds"
      formDescription=""
      subCategories={subCategories}
      onContinue={handleContinue}
      onBack={handleBack}
      onSaveExit={handleSaveAndExit}
    />
  );
};

export default CompoundComponent;
