import {
  PLAN_DESIGNS_BASE_PATH,
  UNBREAKABLE_ITEM,
  XML_ITEM,
} from 'apps/admin-portal/components/benAdmin';
import { getIndexFromURL } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/SideNavBar/parameterUtils';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import CostShareComponent from '../costShareComponent';

const StandardComponent: React.FC<{
  formMethods: UseFormReturn<any>;
  onUpdateActiveItem?: (id: string) => void;
}> = ({ formMethods, onUpdateActiveItem }) => {
  const urlIndex = getIndexFromURL() || 0;
  const basePath = `${PLAN_DESIGNS_BASE_PATH}.${urlIndex}.plan_design_details.[0].cost_share_design`;

  return (
    <CostShareComponent
      formMethods={formMethods}
      onUpdateActiveItem={onUpdateActiveItem}
      isUnbreakable={false}
      title="Standard"
      description=""
      modalTitle="Select Standard Cost Share Tiers"
      basePath={basePath}
      backIndicator={XML_ITEM}
      continueIndicator={UNBREAKABLE_ITEM}
    />
  );
};

export default StandardComponent;
