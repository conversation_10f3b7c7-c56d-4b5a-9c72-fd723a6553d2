import { PLAN_DESIGNS_BASE_PATH } from 'apps/admin-portal/components/benAdmin/organization/components/Tabs/PlanDesign/PlanDesign/Config/coreConfig';
import { getIndexFromURL } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/SideNavBar/parameterUtils';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import {
  COMPOUND_ITEM,
  STANDARD_ITEM,
} from '../../../../../../Navigation/navigationConstants';
import CostShareComponent from '../costShareComponent';

const UnbreakableComponent: React.FC<{
  formMethods: UseFormReturn<any>;
  onUpdateActiveItem?: (id: string) => void;
}> = ({ formMethods, onUpdateActiveItem }) => {
  const urlIndex = getIndexFromURL() || 0;
  const basePath = `${PLAN_DESIGNS_BASE_PATH}.${urlIndex}.plan_design_details.[0].cost_share_design`;

  return (
    <CostShareComponent
      formMethods={formMethods}
      onUpdateActiveItem={onUpdateActiveItem}
      isUnbreakable={true}
      title="Unbreakable"
      description=""
      modalTitle="Select Unbreakable Cost Share Tiers"
      basePath={basePath}
      sourceLabel="Use the same information as in Patient Pay - Standard"
      backIndicator={STANDARD_ITEM}
      continueIndicator={COMPOUND_ITEM}
    />
  );
};

export default UnbreakableComponent;
