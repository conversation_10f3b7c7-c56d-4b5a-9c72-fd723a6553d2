import { useBenAdmin } from 'apps/admin-portal/app/_hooks/useBenAdmin';
import {
  ACCUMULATORS_GENERAL_ITEM,
  COMPOUND_ITEM,
  useSaveChangeRequestHandler,
} from 'apps/admin-portal/components/benAdmin';
import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { createPlanDesignSubmitHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/PlanDesign/planSubmitHandlers';
import GenericForm from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/GenericForm';
import React, { useCallback } from 'react';
import { UseFormReturn } from 'react-hook-form';

import { useDispenseAsWrittenForm } from './dispenseForm';

interface ClientInformationComponentProps {
  formMethods: UseFormReturn<any>;
  onUpdateActiveItem?: (id: string) => void;
}

const DispenseComponent: React.FC<ClientInformationComponentProps> = ({
  formMethods,
  onUpdateActiveItem,
}) => {
  const { watch, getValues } = formMethods;
  const currentDetails = watch();

  const { useApiQuery } = useBenAdmin();
  const { picklist: dispenseAsWritten } = useApiQuery([
    {
      key: 'picklist',
      queryParams: { names: 'DispenseAsWritten' },
      options: {
        staleTime: 5 * 60 * 1000, // 5 minutes
        cacheTime: 10 * 60 * 1000, // 10 minutes
      },
    },
  ]);

  console.log(dispenseAsWritten);

  const baseSaveHandler = useSaveChangeRequestHandler(formMethods);

  // Create an enhanced submit handler that handles multi-plan updates
  const submitHandler = createPlanDesignSubmitHandler(baseSaveHandler);

  // Handle save and exit action
  const handleSaveAndExit = useCallback(() => {
    const currentValues = getValues();
    submitHandler(currentValues);
  }, [getValues, submitHandler]);
  // Build form subCategories and get the original continue/back handlers.
  const { subCategories } = useDispenseAsWrittenForm(
    currentDetails as Partial<OrganizationDetails>
  );

  // When the user clicks "Continue", run any internal continue logic then update the active menu item.
  const handleContinue = () => {
    if (onUpdateActiveItem) {
      onUpdateActiveItem(ACCUMULATORS_GENERAL_ITEM);
    }
  };

  const handleBack = () => {
    if (onUpdateActiveItem) {
      onUpdateActiveItem(COMPOUND_ITEM);
    }
  };

  return (
    <GenericForm
      formMethods={formMethods}
      formName="General"
      formDescription=""
      subCategories={subCategories}
      onContinue={handleContinue}
      onBack={handleBack}
      onSaveExit={handleSaveAndExit}
    />
  );
};

export default DispenseComponent;
