import { useBenAdmin } from 'apps/admin-portal/app/_hooks/useBenAdmin';
import { getDispenseAsWrittenPaths } from 'apps/admin-portal/components/benAdmin';
import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import {
  defineForm<PERSON>ield,
  defineInlineFieldGroup,
  defineSubCategory,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';
import { getIndexFromURL } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/SideNavBar/parameterUtils';
import { SubCategoryType } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import { z } from 'zod';

export interface DispenseAsWrittenOption {
  value: string;
  label: string;
}

/**
 * useFormDemo Hook
 *
 * Generates form subcategories (sections) based on the provided organization details.
 * Uses dot notation for field names so that the final form JSON is nested.
 *
 * @param org - Partial organization data used to prefill form fields.
 * @returns An object containing the generated subCategories and navigation handlers.
 */
export function useDispenseAsWrittenForm(
  currentDetails: Partial<OrganizationDetails>,
  index = 0
) {
  const urlIndex = getIndexFromURL();
  const indexToUse = urlIndex !== undefined ? urlIndex : index;
  const designDetails =
    currentDetails.plan?.plan_designs?.[indexToUse]?.plan_design_details?.[0];

  // Use existing config with the determined index
  const dispenseConfig = getDispenseAsWrittenPaths(indexToUse);

  const { useApiQuery } = useBenAdmin();
  const { picklist: dispenseAsWritten } = useApiQuery([
    {
      key: 'picklist',
      queryParams: { names: 'DispenseAsWritten' },
      options: {
        staleTime: 5 * 60 * 1000, // 5 minutes
        cacheTime: 10 * 60 * 1000, // 10 minutes
      },
    },
  ]);

  // Transform the dispenseAsWritten array into an options map
  const dispenseOptions = (dispenseAsWritten?.DispenseAsWritten || []).reduce(
    (acc: Record<string, string>, item: DispenseAsWrittenOption) => {
      acc[item.value] = item.label;
      return acc;
    },
    {}
  );

  const subCategories: SubCategoryType[] = [
    defineSubCategory('', '', [
      defineInlineFieldGroup([
        defineFormField(
          'Dispense as Written',
          'dropdownSelect',
          dispenseConfig.dispense_as_written_ind,
          designDetails?.dispense_as_written_ind,
          {
            isRequired: true,
            optionsMap: dispenseOptions,
          }
        ),
        defineFormField(
          'Dispense as Written Description',
          'input',
          dispenseConfig.dispense_as_written_description,
          designDetails?.dispense_as_written_description,
          {
            infoText:
              'Which copay structure is used and whether a penalty will apply, based on the Rx DAW code as specified by the prescriber.',
            placeholder: 'Enter Description',
            validations: z.string().max(255),
          }
        ),
      ]),
    ]),
  ];

  return {
    subCategories,
  };
}
