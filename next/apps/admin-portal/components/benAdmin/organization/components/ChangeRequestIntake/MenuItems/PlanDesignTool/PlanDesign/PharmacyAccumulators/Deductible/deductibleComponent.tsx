import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { useSaveChangeRequestHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/useOrganizationHooks';
import GenericForm from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/GenericForm';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import {
  ACCUMULATORS_GENERAL_ITEM,
  MAXIMUM_OUT_OF_POCKET_ITEM,
} from '../../../../../Navigation/navigationConstants';
import { useDeductibleForm } from './deductibleForm';

interface ClientInformationComponentProps {
  formMethods: UseFormReturn<any>;
  onUpdateActiveItem?: (id: string) => void;
}

const DeductibleComponent: React.FC<ClientInformationComponentProps> = ({
  formMethods,
  onUpdateActiveItem,
}) => {
  const { watch } = formMethods;
  const currentDetails = watch();

  const submitHandler = useSaveChangeRequestHandler(formMethods);

  const handleSaveAndExit = () => {
    // Submit the current form data
    const currentValues = formMethods.getValues();
    submitHandler(currentValues);
  };

  // Build form subCategories and get the original continue/back handlers.
  const { subCategories } = useDeductibleForm(
    currentDetails as Partial<OrganizationDetails>,
    0
  );

  const handleBack = () => {
    if (onUpdateActiveItem) {
      onUpdateActiveItem(ACCUMULATORS_GENERAL_ITEM);
    }
  };

  const handleContinue = () => {
    if (onUpdateActiveItem) {
      onUpdateActiveItem(MAXIMUM_OUT_OF_POCKET_ITEM);
    }
  };

  return (
    <GenericForm
      formMethods={formMethods}
      formName="Pharmacy Accumulators - Deductible"
      formDescription=""
      subCategories={subCategories}
      onSaveExit={handleSaveAndExit}
      onContinue={handleContinue}
      onBack={handleBack}
    />
  );
};

export default DeductibleComponent;
