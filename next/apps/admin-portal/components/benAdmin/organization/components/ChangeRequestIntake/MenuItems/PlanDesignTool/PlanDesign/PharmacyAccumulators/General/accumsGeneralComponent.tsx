import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { useSaveChangeRequestHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/useOrganizationHooks';
import GenericForm from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/GenericForm';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import {
  DEDUCTIBLE_ITEM,
  DISPENSE_ITEM,
} from '../../../../../Navigation/navigationConstants';
import { useAccumsGeneralForm } from './accumsGeneralForm';

interface ClientInformationComponentProps {
  formMethods: UseFormReturn<any>;
  onUpdateActiveItem?: (id: string) => void;
}

const AccumsGeneralComponent: React.FC<ClientInformationComponentProps> = ({
  formMethods,
  onUpdateActiveItem,
}) => {
  const { watch } = formMethods;
  const currentDetails = watch();

  const submitHandler = useSaveChangeRequestHandler(formMethods);

  const handleSaveAndExit = () => {
    // Submit the current form data
    const currentValues = formMethods.getValues();
    submitHandler(currentValues);
  };
  // Build form subCategories and get the original continue/back handlers.
  const { subCategories } = useAccumsGeneralForm(
    currentDetails as Partial<OrganizationDetails>,
    0
  );

  const handleBack = () => {
    if (onUpdateActiveItem) {
      onUpdateActiveItem(DISPENSE_ITEM);
    }
  };

  const handleContinue = () => {
    if (onUpdateActiveItem) {
      onUpdateActiveItem(DEDUCTIBLE_ITEM);
    }
  };

  return (
    <GenericForm
      formMethods={formMethods}
      formName="Pharmacy Accumulators - General"
      formDescription=""
      subCategories={subCategories}
      onSaveExit={handleSaveAndExit}
      onContinue={handleContinue}
      onBack={handleBack}
    />
  );
};

export default AccumsGeneralComponent;
