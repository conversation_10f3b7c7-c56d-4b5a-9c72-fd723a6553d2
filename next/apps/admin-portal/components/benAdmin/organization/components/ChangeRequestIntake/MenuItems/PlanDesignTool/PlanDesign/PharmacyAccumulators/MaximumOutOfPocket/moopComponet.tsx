import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { useSaveChangeRequestHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/useOrganizationHooks';
import GenericForm from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/GenericForm';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import {
  DEDUCTIBLE_ITEM,
  OTHER_CAP_ITEM,
} from '../../../../../Navigation/navigationConstants';
import { useMoopForm } from './moopForm';

interface ClientInformationComponentProps {
  formMethods: UseFormReturn<any>;
  onUpdateActiveItem?: (id: string) => void;
}

const MoopComponent: React.FC<ClientInformationComponentProps> = ({
  formMethods,
  onUpdateActiveItem,
}) => {
  const { watch } = formMethods;
  const currentDetails = watch();

  const submitHandler = useSaveChangeRequestHandler(formMethods);

  const handleSaveAndExit = () => {
    // Submit the current form data
    const currentValues = formMethods.getValues();
    submitHandler(currentValues);
  };

  // Build form subCategories and get the original continue/back handlers.
  const { subCategories } = useMoopForm(
    currentDetails as Partial<OrganizationDetails>,
    0
  );

  const handleBack = () => {
    if (onUpdateActiveItem) {
      onUpdateActiveItem(DEDUCTIBLE_ITEM);
    }
  };

  const handleContinue = () => {
    if (onUpdateActiveItem) {
      onUpdateActiveItem(OTHER_CAP_ITEM);
    }
  };

  return (
    <GenericForm
      formMethods={formMethods}
      formName="Pharmacy Accumulators - Maximum Out of Pocket"
      formDescription=""
      subCategories={subCategories}
      onSaveExit={handleSaveAndExit}
      onContinue={handleContinue}
      onBack={handleBack}
    />
  );
};

export default MoopComponent;
