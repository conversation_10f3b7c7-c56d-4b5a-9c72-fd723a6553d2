import { getAccumsGeneralPaths } from 'apps/admin-portal/components/benAdmin/organization/components/Tabs/PlanDesign/PlanDesign/Config/accumsGeneralConfig';
import { useSaveChangeRequestHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/useOrganizationHooks';
import DynamicCollectionSection from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/DynamicCollectionSection';
import { getIndexFromURL } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/SideNavBar/parameterUtils';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import {
  MAXIMUM_OUT_OF_POCKET_ITEM,
  PLAN_DESIGN_REVIEW_ITEM,
} from '../../../../../Navigation/navigationConstants';
import { OtherCapForm } from './otherCapForm';

const OtherCapComponent: React.FC<{
  formMethods: UseFormReturn<any>;
  onUpdateActiveItem?: (id: string) => void;
}> = ({ formMethods, onUpdateActiveItem }) => {
  const submitHandler = useSaveChangeRequestHandler(formMethods);
  const urlIndex = getIndexFromURL() || 0;

  const handleSaveAndExit = () => {
    // Submit the current form data
    const currentValues = formMethods.getValues();
    submitHandler(currentValues);
  };

  const handleNavigation = (destination: string) => {
    if (onUpdateActiveItem) {
      onUpdateActiveItem(destination);
    }
  };

  return (
    <DynamicCollectionSection
      basePath={getAccumsGeneralPaths(urlIndex).accumulation_other}
      formMethods={formMethods}
      generalForm={<OtherCapForm />}
      title="Pharmacy Accumulators - Other Cap"
      description=""
      emptyMessage="You haven't added anything."
      skipMessage="If this Plan does not have any In-House Pharmacies, skip to the next step."
      isOptional={true}
      modalTitle="Pharmacy Accumulators - Other Cap"
      onBack={() => handleNavigation(MAXIMUM_OUT_OF_POCKET_ITEM)}
      onContinue={() => handleNavigation(PLAN_DESIGN_REVIEW_ITEM)}
      onSaveExit={handleSaveAndExit}
    />
  );
};

export default OtherCapComponent;
