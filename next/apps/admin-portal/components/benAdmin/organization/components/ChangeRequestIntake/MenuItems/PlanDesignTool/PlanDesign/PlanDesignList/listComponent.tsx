import { useToast } from '@chakra-ui/react';
import { useBenAdmin } from 'apps/admin-portal/app/_hooks/useBenAdmin';
import {
  GENERAL_ITEM,
  PLAN_DESIGNS_BASE_PATH,
  PlanDesign,
} from 'apps/admin-portal/components/benAdmin';
import { ConfirmationPopup } from 'apps/admin-portal/components/benAdmin/organization/components/ConfirmationPopup';
import { useSaveChangeRequestHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/useOrganizationHooks';
import DynamicCollectionSection from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/DynamicCollectionSection';
import React, { useEffect, useState } from 'react';
import { UseFormReturn } from 'react-hook-form';

// URL utilities for handling URL parameters
const urlUtils = {
  // Update the edit mode parameter
  updateEditMode: (isEdit: boolean): void => {
    const currentUrl = new URL(window.location.href);
    if (isEdit) {
      currentUrl.searchParams.set('edit', 'true');
    } else {
      currentUrl.searchParams.delete('edit');
    }
    window.history.replaceState({}, '', currentUrl.toString());
  },

  // Clean URL by keeping only specific parameters and ensuring edit=false
  cleanUrlParams: (): void => {
    const currentUrl = new URL(window.location.href);
    const paramsToKeep = ['tab', 'section'];

    // Create a new URLSearchParams with only the parameters we want to keep
    const newParams = new URLSearchParams();

    // Only add parameters that exist and are in our list
    paramsToKeep.forEach((param) => {
      if (currentUrl.searchParams.has(param)) {
        newParams.set(param, currentUrl.searchParams.get(param) || '');
      }
    });

    // Always set edit=false for this page
    newParams.set('edit', 'false');

    // Update the URL
    currentUrl.search = newParams.toString();
    window.history.replaceState({}, '', currentUrl.toString());
  },

  // Add the updateParams method
  updateParams: (params: Record<string, string | null>): void => {
    const currentUrl = new URL(window.location.href);
    Object.entries(params).forEach(([key, value]) => {
      if (value === null) {
        currentUrl.searchParams.delete(key);
      } else {
        currentUrl.searchParams.set(key, value);
      }
    });
    window.history.replaceState({}, '', currentUrl.toString());
  },
};

/**
 * PlanListComponent that uses the DynamicCollectionSection
 */
const PlanListComponent: React.FC<{
  formMethods: UseFormReturn<any>;
  onUpdateActiveItem?: (id: string) => void;
}> = ({ formMethods, onUpdateActiveItem }) => {
  const [isCreating, setIsCreating] = useState(false);
  const [showWarningModal, setShowWarningModal] = useState(false);
  const [pendingPlanDesign, setPendingPlanDesign] = useState<PlanDesign | null>(
    null
  );
  const toast = useToast();
  const submitHandler = useSaveChangeRequestHandler(formMethods);
  const plan_id = formMethods.getValues('plan.plan_id');
  const { setValue } = formMethods;

  const { useApiMutation } = useBenAdmin();

  // Use mutation for creating plan design
  const { mutateAsync: createPlanDesign } = useApiMutation(
    'planDesign',
    'POST',
    {
      // Request for complete data structure in response
      include: ['plan_design_details', 'plan_features'],
    }
  );

  const hasExistingPlanDesigns =
    formMethods.getValues(PLAN_DESIGNS_BASE_PATH).length > 0;

  // Function to check if a plan design has null values
  const hasNullValues = (planDesign: PlanDesign): boolean => {
    const checkForNulls = (obj: any): boolean => {
      if (obj === null) return true;
      if (typeof obj !== 'object') return false;

      if (Array.isArray(obj)) {
        return obj.some((item) => checkForNulls(item));
      }

      return Object.values(obj).some((value) => checkForNulls(value));
    };

    return checkForNulls(planDesign);
  };

  // Function to handle the warning modal confirmation
  const handleWarningConfirm = async () => {
    setShowWarningModal(false);
    if (pendingPlanDesign) {
      await proceedWithPlanDesignCreation(pendingPlanDesign);
    }
  };

  // Function to proceed with plan design creation after warning
  const proceedWithPlanDesignCreation = async (newPlanDesign: PlanDesign) => {
    try {
      // Get current plan designs array
      const currentPlanDesigns =
        formMethods.getValues(PLAN_DESIGNS_BASE_PATH) || [];

      // Generate a unique name for the new plan design
      if (!newPlanDesign.name) {
        const baseName = 'New Plan Design';
        let nameToUse = baseName;
        let counter = 2;

        const existingNames = currentPlanDesigns.map(
          (design: PlanDesign) => design.name
        );

        while (existingNames.includes(nameToUse)) {
          nameToUse = `${baseName} ${counter}`;
          counter++;
        }

        newPlanDesign.name = nameToUse;
        newPlanDesign.active_ind = 1;
        newPlanDesign.plan_design_details[0].accumulation_deductible[0].apply_ind = 1;
        newPlanDesign.plan_design_details[0].accumulation_moop[0].apply_ind = 1;
      }

      // Add the new plan design to the existing array
      const updatedPlanDesigns = [...currentPlanDesigns, newPlanDesign];
      const newIndex = updatedPlanDesigns.length - 1;

      // Set the updated array back to the form
      setValue(PLAN_DESIGNS_BASE_PATH, updatedPlanDesigns, {
        shouldValidate: true,
        shouldDirty: true,
      });

      toast({
        title: 'Plan design created successfully',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });

      urlUtils.updateParams({ index: newIndex.toString() });
      await handleNavigation(GENERAL_ITEM);
      urlUtils.updateEditMode(true);
    } catch (error) {
      console.error('Failed to create plan design:', error);
      toast({
        title: 'Failed to create plan design',
        description:
          error instanceof Error ? error.message : 'An unknown error occurred',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
      throw error;
    } finally {
      setIsCreating(false);
      setPendingPlanDesign(null);
    }
  };

  // Function to create a new plan design via API
  const handleCreatePlanDesign = async () => {
    if (isCreating) return;

    setIsCreating(true);
    try {
      const initialData = {
        plan_id,
      };

      const response = await createPlanDesign({
        pathParams: { id: plan_id },
        ...initialData,
      });

      const convertZerosToNulls = (obj: any): any => {
        if (obj === null || obj === undefined) return obj;
        if (obj === 0) return null;

        if (typeof obj !== 'object') return obj;

        if (Array.isArray(obj)) {
          return obj.map((item) => convertZerosToNulls(item));
        }

        const result: Record<string, any> = {};
        for (const key in obj) {
          if (Object.prototype.hasOwnProperty.call(obj, key)) {
            result[key] = convertZerosToNulls(obj[key]);
          }
        }

        return result;
      };

      const rawPlanDesign = response.plan_designs[0];
      const newPlanDesign = convertZerosToNulls(rawPlanDesign) as PlanDesign;

      // Check if the plan design has null values
      if (hasExistingPlanDesigns && hasNullValues(newPlanDesign)) {
        setPendingPlanDesign(newPlanDesign);
        setShowWarningModal(true);
        return;
      }

      // If no null values, proceed with creation
      await proceedWithPlanDesignCreation(newPlanDesign);
    } catch (error) {
      console.error('Failed to create plan design:', error);
      toast({
        title: 'Failed to create plan design',
        description:
          error instanceof Error ? error.message : 'An unknown error occurred',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
      setIsCreating(false);
      throw error;
    }
  };

  // Clean up URL parameters when component mounts
  useEffect(() => {
    urlUtils.cleanUrlParams();
  }, []);

  /**
   * Handle save and exit action
   */
  const handleSaveAndExit = () => {
    // Submit the current form data
    const currentValues = formMethods.getValues();
    submitHandler(currentValues);
  };

  /**
   * Handle navigation to a different section
   */
  const handleNavigation = (destination: string) => {
    if (onUpdateActiveItem) {
      onUpdateActiveItem(destination);
    }
  };

  /**
   * Custom handler for "Add New" button
   */
  const handleAddItem = async () => {
    try {
      await handleCreatePlanDesign();
      // Navigation handled in handleCreatePlanDesign
    } catch (error) {
      // Error handling done in handleCreatePlanDesign
    }
  };

  /**
   * Custom handler for "Edit" button
   */
  const handleEditItem = (index: number) => {
    // Navigate to the GENERAL_ITEM
    handleNavigation(GENERAL_ITEM);

    // Update the edit mode to true
    urlUtils.updateEditMode(true);

    // Also update the index parameter
    urlUtils.updateParams({ index: index.toString() });
  };

  return (
    <>
      <DynamicCollectionSection
        basePath={PLAN_DESIGNS_BASE_PATH}
        formMethods={formMethods}
        addCustomVars={[['plan_id', plan_id]]}
        title="Plan Design List"
        description=""
        emptyMessage="You haven't added anything."
        skipMessage="To get started, create a new Plan Design option to start customizing it."
        modalTitle="Pharmacy Accumulators - Other Cap"
        onSaveExit={handleSaveAndExit}
        createEmptyObject={false}
        onAddItem={handleAddItem}
        editButtonText="Edit Plan"
        onEditItem={handleEditItem}
        useAccordion={true}
        isAddButtonLoading={isCreating}
        hideContinueButton={true}
      />

      <ConfirmationPopup
        isOpen={showWarningModal}
        onClose={handleWarningConfirm}
        onConfirm={handleWarningConfirm}
        alertHeader="Plan Design Fields Differ"
        alertBody="Some fields differ between your plan designs. To prevent these fields from appearing blank, please update the Clinical Design."
        confirmButtonText="Continue"
        hideCancelButton={true}
      />
    </>
  );
};

export default PlanListComponent;
