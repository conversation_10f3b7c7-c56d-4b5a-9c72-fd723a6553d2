import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { useSaveChangeRequestHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/useOrganizationHooks';
import GenericForm from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/GenericForm';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import { PHARMACY_NETWORK_ITEM } from '../../../../../Navigation/navigationConstants';
import { useProductSetupForm } from './productSetupForm';

interface ClientInformationComponentProps {
  formMethods: UseFormReturn<any>;
  onUpdateActiveItem?: (id: string) => void;
}

const ProductSetupComponent: React.FC<ClientInformationComponentProps> = ({
  formMethods,
  onUpdateActiveItem,
}) => {
  const { watch } = formMethods;
  const currentDetails = watch();

  const submitHandler = useSaveChangeRequestHandler(formMethods);

  const handleSaveAndExit = () => {
    // Submit the current form data
    const currentValues = formMethods.getValues();
    submitHandler(currentValues);
  };
  // Build form subCategories and get the original continue/back handlers.
  const { subCategories } = useProductSetupForm(
    currentDetails as Partial<OrganizationDetails>
  );

  // When the user clicks "Continue", run any internal continue logic then update the active menu item.
  const handleContinue = () => {
    // Seamlessly update the active menu item to "pharmacy-benefits-manager" via the callback.
    if (onUpdateActiveItem) {
      onUpdateActiveItem(PHARMACY_NETWORK_ITEM);
    }
  };

  return (
    <GenericForm
      formMethods={formMethods}
      formName="Core Products - Product Set Up"
      formDescription=""
      subCategories={subCategories}
      onContinue={handleContinue}
      onSaveExit={handleSaveAndExit}
    />
  );
};

export default ProductSetupComponent;
