// useProductsAndServicesSections.ts
import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import {
  FormSections,
  SectionConfig,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/Completion/types';

import {
  generateProductConfigs,
  organizePlanDesignAncillaries,
} from '../../../../../Tabs/PlanDesign/ProductsAndServices/Config/productConfig';
import {
  CORE_PRODUCTS_ITEM,
  PRODUCTS_AND_SERVICES_MODE,
} from '../../../../Navigation/navigationConstants';
import { useProductSetupForm } from '../CoreProducts/ProductsSetup/productSetupForm';

export function useProductsAndServicesSections(
  currentDetails?: Partial<OrganizationDetails>
): FormSections {
  const { subCategories: productsAndSetupSubCategorys } = useProductSetupForm(
    currentDetails || {}
  );

  // Get plan design ancillaries and organize them into buckets for each plan design
  const planDesigns = currentDetails?.plan?.plan_designs || [];
  const dynamicSections: SectionConfig[] = planDesigns.flatMap(
    (planDesign, planDesignIndex) => {
      const productBuckets = organizePlanDesignAncillaries(
        planDesign.plan_design_ancillaries
      );
      const productConfigs = generateProductConfigs(productBuckets);
      return productConfigs.map((config) => ({
        id: `${config.tabId}-pd${planDesignIndex}`,
        title: `${config.subtitle} (Design ${planDesignIndex + 1})`,
        path: `plan.plan_designs.${planDesignIndex}.plan_design_ancillaries`,
        isCollection: true,
      }));
    }
  );

  const sections: SectionConfig[] = [
    {
      id: CORE_PRODUCTS_ITEM,
      title: 'Core Products',
      relatedSections: [...productsAndSetupSubCategorys],
      isCollection: false,
    },
    ...dynamicSections,
  ];

  return { sections, id: PRODUCTS_AND_SERVICES_MODE };
}
