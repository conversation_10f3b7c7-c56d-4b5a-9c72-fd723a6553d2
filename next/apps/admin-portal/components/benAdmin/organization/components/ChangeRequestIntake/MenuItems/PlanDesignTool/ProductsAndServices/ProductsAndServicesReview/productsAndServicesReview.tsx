// ReviewProductsAndServices.tsx
import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { useSaveChangeRequestHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/useOrganizationHooks';
import FormReviewSummary from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/FormReviewSummary';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import {
  CORE_PRODUCTS_ITEM,
  PRODUCT_SET_UP_ITEM,
} from '../../../../Navigation/navigationConstants';
import { useProductsAndServicesSections } from './productAndServicesSections';

interface ReviewProps {
  formMethods: UseFormReturn<any>;
  onUpdateActiveItem?: (id: string) => void;
  currentDetails?: Partial<OrganizationDetails>;
  lastSectionId?: string;
}

const ReviewProductsAndServices: React.FC<ReviewProps> = ({
  formMethods,
  onUpdateActiveItem,
  currentDetails,
  lastSectionId,
}) => {
  const saveHandler = useSaveChangeRequestHandler(formMethods);
  const formSections = useProductsAndServicesSections(currentDetails);

  const handleNavigation = (sectionId?: string) => {
    if (onUpdateActiveItem) {
      if (sectionId === CORE_PRODUCTS_ITEM) {
        onUpdateActiveItem(PRODUCT_SET_UP_ITEM);
      } else {
        onUpdateActiveItem(sectionId || lastSectionId || PRODUCT_SET_UP_ITEM);
      }
    }
  };

  const handleSaveExit = () => {
    const formData = formMethods.getValues();
    saveHandler(formData);
  };

  return (
    <FormReviewSummary
      title="Review and Save Changes"
      description=""
      subtitle="Products & Services"
      formMethods={formMethods}
      formSections={formSections}
      onBack={handleNavigation}
      onSaveExit={handleSaveExit}
    />
  );
};

export default ReviewProductsAndServices;
