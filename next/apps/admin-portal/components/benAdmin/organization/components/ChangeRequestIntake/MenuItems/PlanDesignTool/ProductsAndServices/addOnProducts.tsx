import {
  Box,
  Center,
  Spinner,
  useDisclosure,
  useToast,
} from '@chakra-ui/react';
import { useBenAdmin } from 'apps/admin-portal/app/_hooks/useBenAdmin';
import {
  ChangeRequest,
  PlanAncillary,
} from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import GenericForm from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/GenericForm';
import { useCallback, useMemo, useState } from 'react';
import { UseFormReturn } from 'react-hook-form';

import { PLAN_DESIGNS_BASE_PATH } from '../../../../Tabs/PlanDesign/PlanDesign/Config/coreConfig';
import { GenericProductForm } from './genericProductForm';

interface AddOnProductsProps {
  planId?: number;
  formMethods?: UseFormReturn<any>;
}

const AddOnProducts = ({ planId, formMethods }: AddOnProductsProps) => {
  const [newAncillaryIndices, setNewAncillaryIndices] = useState<number[]>([]);
  const [showProductForm, setShowProductForm] = useState(false);
  const changeRequest: ChangeRequest | null = JSON.parse(
    sessionStorage.getItem('selectedChangeRequest') || 'null'
  );

  const requestId = changeRequest?.change_request_id || '';
  const {
    isOpen: isOverlayVisible,
    onOpen: showOverlay,
    onClose: hideOverlay,
  } = useDisclosure();
  const toast = useToast();
  const { useApiQuery, useApiMutation } = useBenAdmin();

  // Watch the ancillaries data (use planDesigns[0])
  const watchedAncillaries = formMethods?.watch(
    `${PLAN_DESIGNS_BASE_PATH}.0.plan_design_ancillaries`
  ) as PlanAncillary[];

  // Create a Set of disabled product IDs from watched ancillaries
  const disabledProductIds = useMemo(() => {
    const safeAncillaries = Array.isArray(watchedAncillaries)
      ? watchedAncillaries
      : [];
    return new Set(
      safeAncillaries
        .map((ancillary) => ancillary.product_id)
        .filter((id): id is number => id !== null)
    );
  }, [watchedAncillaries]);

  const { addOnProduct, isLoading } = useApiQuery([
    {
      key: 'addOnProduct',
      pathParams: { id: planId },
      options: {
        staleTime: 5 * 60 * 1000,
        cacheTime: 10 * 60 * 1000,
      },
    },
  ]);

  //   // Use mutation for creating plan ancillaries
  //   const { mutateAsync: createPlanAncillaries } = useApiMutation(
  //     'planDesignAncillary',
  //     'POST'
  //   );

  const { mutateAsync: getPlanDesigns } = useApiMutation(
    'changeContent',
    'POST'
  );

  const [isProcessing, setIsProcessing] = useState(false);

  const handleContinue = useCallback(
    async (selectedItems: any) => {
      if (!planId || !formMethods) return;

      try {
        setIsProcessing(true);
        showOverlay();
        const response = await getPlanDesigns({
          pathParams: { id: requestId },
          ...selectedItems,
        });
        const planDesigns = response.plan_designs || [];

        if (planDesigns.length === 0) {
          toast({
            title: 'No Plan Designs Detected',
            description:
              'We were not able to create these add-on products because no plan designs were detected.',
            status: 'error',
            duration: 6000,
            isClosable: true,
            position: 'top',
          });
          return;
        }

        // Get current plan designs from form
        const currentPlanDesigns =
          formMethods.getValues(PLAN_DESIGNS_BASE_PATH) || [];
        const backendPlanDesigns = planDesigns || [];

        // Get new ancillaries from backendPlanDesigns[0]
        const backendAncillaries = Array.isArray(
          backendPlanDesigns[0]?.plan_design_ancillaries
        )
          ? backendPlanDesigns[0].plan_design_ancillaries
          : [];

        console.log('backendAncillaries', backendAncillaries);

        // Add these new ancillaries to all plan designs in the form, skipping duplicates
        let firstPlanNewIndices: number[] = [];
        const updatedPlanDesigns = currentPlanDesigns.map(
          (formPlan: any, idx: number) => {
            const formAncillaries = Array.isArray(
              formPlan.plan_design_ancillaries
            )
              ? formPlan.plan_design_ancillaries
              : [];
            const existingProductIds = new Set(
              formAncillaries.map((a: any) => a.product_id)
            );
            const newAncillaries = backendAncillaries.filter(
              (anc: any) => !existingProductIds.has(anc.product_id)
            );
            // For the first plan design, record the indices where new ancillaries are added
            let plan_design_ancillaries;
            if (idx === 0) {
              plan_design_ancillaries = [...formAncillaries, ...newAncillaries];
              firstPlanNewIndices = newAncillaries.map(
                (_: any, i: number) => formAncillaries.length + i
              );
            } else {
              plan_design_ancillaries = [...formAncillaries, ...newAncillaries];
            }
            return {
              ...formPlan,
              plan_design_ancillaries,
            };
          }
        );

        formMethods.setValue(PLAN_DESIGNS_BASE_PATH, updatedPlanDesigns, {
          shouldDirty: true,
          shouldTouch: true,
          shouldValidate: true,
        });

        setNewAncillaryIndices(firstPlanNewIndices);
        setShowProductForm(true);
      } catch (error) {
        console.error('Error creating plan ancillaries:', error);
      } finally {
        setIsProcessing(false);
        hideOverlay();
      }
    },
    [
      planId,
      formMethods,
      showOverlay,
      getPlanDesigns,
      requestId,
      toast,
      hideOverlay,
    ]
  );

  const handleSave = useCallback((data: PlanAncillary) => {
    // All ancillaries are completed, you can handle the final save here
    console.log('All ancillaries completed:', data);
    // You might want to navigate away or show a success message
  }, []);

  if (isLoading) {
    return (
      <Box mb={6}>
        <Center py={10}>
          <Spinner size="xl" color="green.500" thickness="4px" />
        </Center>
      </Box>
    );
  }

  if (showProductForm) {
    if (!formMethods) {
      console.error('Form methods not available');
      return null;
    }

    return (
      <GenericProductForm
        formMethods={formMethods}
        productType="add-on"
        isModal={false}
        newAncillaryIndices={newAncillaryIndices}
        onSave={handleSave}
        onCancel={() => setShowProductForm(false)}
      />
    );
  }

  return (
    <>
      {isOverlayVisible && (
        <Box
          position="fixed"
          top={0}
          left={0}
          right={0}
          bottom={0}
          bg="blackAlpha.300"
          zIndex={1000}
          onClick={(e) => e.preventDefault()}
        />
      )}
      <GenericForm
        formName="Which products are you adding to this plan?"
        formDescription=""
        subCategories={[]}
        multiSelectData={{
          data: addOnProduct,
          label: 'Select product add-ons',
          tag: 'product_class_name',
          disabledItems: disabledProductIds,
        }}
        onContinue={handleContinue}
        formMethods={formMethods}
        showButtons={true}
        showSubmit={false}
        isProcessing={isProcessing}
      />
    </>
  );
};

export default AddOnProducts;
