import {
  ADD_ON_PRODUCTS_ITEM,
  GenericProductForm,
  PLAN_DESIGNS_BASE_PATH,
} from 'apps/admin-portal/components/benAdmin';
import { PlanDesignAncillary } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { useSaveChangeRequestHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/useOrganizationHooks';
import DynamicCollectionSection from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/DynamicCollectionSection';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { UseFormReturn, useWatch } from 'react-hook-form';

interface GenericProductComponentProps {
  formMethods: UseFormReturn<any>;
  planDesignIndex: number;
  onUpdateActiveItem?: (id: string) => void;
  title: string;
  description?: string;
  basePath: string;
  emptyMessage?: string;
  skipMessage?: string;
  isOptional?: boolean;
  modalTitle?: string;
  onBack?: () => void;
  onSkip?: () => void;
  productType: string;
}

/**
 * Component for managing a dynamic collection of product ancillaries for a specific plan design.
 * Handles filtering, editing, and deleting of product ancillaries by product type.
 *
 * @param formMethods - react-hook-form methods for form state management
 * @param planDesignIndex - Index of the plan design in the form state
 * @param onUpdateActiveItem - Callback to update the active sidebar item
 * @param title - Title for the section
 * @param description - Description for the section
 * @param basePath - Base path for form state
 * @param emptyMessage - Message to display when no items are present
 * @param skipMessage - Message to display for skipping
 * @param isOptional - Whether the section is optional
 * @param onBack - Callback for back navigation
 * @param onSkip - Callback for skip navigation
 * @param productType - The product type to filter by
 */
const GenericProductComponent: React.FC<GenericProductComponentProps> = ({
  formMethods,
  planDesignIndex,
  onUpdateActiveItem,
  title,
  description,
  emptyMessage = "You haven't added anything.",
  skipMessage = 'If this Plan does not have any products, skip to the next step.',
  isOptional = true,
  onBack,
  onSkip,
  productType,
}) => {
  // Custom hook to handle saving the change request
  const submitHandler = useSaveChangeRequestHandler(formMethods);

  // Watch ancillaries for changes (now nested)
  const watchedAncillaries = useWatch({
    control: formMethods.control,
    name: `${PLAN_DESIGNS_BASE_PATH}.${planDesignIndex}.plan_design_ancillaries`,
    defaultValue: [],
  });

  /**
   * Filters ancillaries by the specified product type.
   * Checks both direct and nested product type fields for compatibility.
   */
  const filterByProductType = useCallback(
    (item: PlanDesignAncillary, idx: number, arr: PlanDesignAncillary[]) => {
      if (!item || typeof item !== 'object') return false;
      const directProductType = item.product_type;
      const nestedProductType = item.product?.product_class?.name;
      const matches =
        directProductType === productType || nestedProductType === productType;
      if (!matches) return false;
      const productId = item.product_id;
      return arr.findIndex((i) => i?.product_id === productId) === idx;
    },
    [productType]
  );

  const filteredItems = useMemo(() => {
    if (!Array.isArray(watchedAncillaries)) return [];
    return watchedAncillaries.filter(filterByProductType);
  }, [filterByProductType, watchedAncillaries]);

  const hasMatchingAncillaries = useMemo(
    () => filteredItems.length > 0,
    [filteredItems]
  );

  useEffect(() => {
    if (!hasMatchingAncillaries && onUpdateActiveItem) {
      onUpdateActiveItem(ADD_ON_PRODUCTS_ITEM);
    }
  }, [hasMatchingAncillaries, onUpdateActiveItem]);

  // Track the actual index of the item being edited
  const [editingIndex, setEditingIndex] = useState<number | undefined>(
    undefined
  );

  /**
   * Handles saving and exiting the form, submitting the current form values.
   */
  const handleSaveAndExit = useCallback(() => {
    const currentValues = formMethods.getValues();
    submitHandler(currentValues);
  }, [formMethods, submitHandler]);

  /**
   * Handler to delete ancillary from all plan designs by product.name and product_type.
   * Ensures consistency across all plan designs.
   */
  const handleDeleteAncillary = useCallback(
    (deletedIndex: number) => {
      const deletedAncillary = watchedAncillaries[deletedIndex];

      // Get the product_id of the ancillary being deleted
      const deletedProductId = deletedAncillary?.product_id;

      // If no product_id, just remove the single item
      if (!deletedProductId) {
        const updatedAncillaries = watchedAncillaries.filter(
          (_: any, idx: number) => idx !== deletedIndex
        );
        formMethods.setValue(
          `${PLAN_DESIGNS_BASE_PATH}.${planDesignIndex}.plan_design_ancillaries`,
          updatedAncillaries
        );
        return;
      }

      // Remove all ancillaries with the same product_id from current plan design
      const updatedAncillaries = watchedAncillaries.filter(
        (anc: any) => anc?.product_id !== deletedProductId
      );

      formMethods.setValue(
        `${PLAN_DESIGNS_BASE_PATH}.${planDesignIndex}.plan_design_ancillaries`,
        updatedAncillaries
      );

      // Remove all ancillaries with the same product_id from all other plan designs
      const allPlanDesigns =
        formMethods.getValues(PLAN_DESIGNS_BASE_PATH) || [];

      allPlanDesigns.forEach((planDesign: any, idx: number) => {
        // Skip the current plan design (already handled above)
        if (idx === planDesignIndex) return;

        // Skip if no ancillaries array
        if (!Array.isArray(planDesign.plan_design_ancillaries)) return;

        // Filter out all ancillaries with matching product_id
        const filtered = planDesign.plan_design_ancillaries.filter(
          (anc: any) => anc?.product_id !== deletedProductId
        );

        // Only update if something was actually removed
        if (filtered.length !== planDesign.plan_design_ancillaries.length) {
          formMethods.setValue(
            `${PLAN_DESIGNS_BASE_PATH}.${idx}.plan_design_ancillaries`,
            filtered
          );
        }
      });
    },
    [watchedAncillaries, formMethods, planDesignIndex]
  );

  /**
   * Memoized form component for editing or creating a product ancillary.
   * Handles modal close and error clearing.
   */
  const generalFormComponent = useMemo(() => {
    // Get the correct item based on whether we're editing or creating new
    const itemToEdit =
      editingIndex !== undefined ? watchedAncillaries[editingIndex] : null;

    const handleModalClose = () => {
      // Reset the editing index and clear any form errors related to this path
      setEditingIndex(undefined);
      formMethods.clearErrors(
        `${PLAN_DESIGNS_BASE_PATH}.${planDesignIndex}.plan_design_ancillaries`
      );
    };

    return (
      <GenericProductForm
        key={`form-${
          editingIndex === undefined ? 'new' : editingIndex
        }-${productType}`}
        formMethods={formMethods}
        initialData={itemToEdit || {}}
        itemIndex={editingIndex}
        productType={productType}
        onCancel={handleModalClose}
        isModal={true}
      />
    );
  }, [
    editingIndex,
    watchedAncillaries,
    productType,
    formMethods,
    planDesignIndex,
  ]);

  /**
   * Handler to set the editing index for the item being edited.
   */
  const handleEditItem = useCallback((index: number) => {
    setEditingIndex(index);
  }, []);

  return (
    <DynamicCollectionSection
      title={title}
      description={description}
      formMethods={formMethods}
      basePath={`${PLAN_DESIGNS_BASE_PATH}.${planDesignIndex}.plan_design_ancillaries`}
      emptyMessage={emptyMessage}
      skipMessage={skipMessage}
      isOptional={isOptional}
      modalTitle={`Add New ${title}`}
      modalSize="xl"
      generalForm={generalFormComponent}
      onBack={onBack}
      onSkip={onSkip}
      onSaveExit={handleSaveAndExit}
      filterItems={filterByProductType}
      customNameField={'product.name'}
      hideAddNewButton={true}
      onRemoveItem={handleDeleteAncillary}
      onEditItem={handleEditItem}
    />
  );
};

export default GenericProductComponent;
