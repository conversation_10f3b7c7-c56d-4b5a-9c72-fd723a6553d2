import {
  createFeatureComponent,
  generateFeatureTabId,
  PLAN_DESIGNS_BASE_PATH,
} from 'apps/admin-portal/components/benAdmin';
import {
  OrganizationDetails,
  PlanDesign,
  PlanDesignAncillary,
} from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { SidebarConfig } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import { UseFormReturn } from 'react-hook-form';

import {
  generateProductConfigs,
  organizePlanDesignAncillaries,
} from '../../../../Tabs/PlanDesign/ProductsAndServices/Config/productConfig';
import {
  ADD_ON_PRODUCTS_ITEM,
  CORE_PRODUCTS_ITEM,
  PHARMACY_NETWORK_ITEM,
  PRODUCT_SET_UP_ITEM,
  PRODUCTS_AND_SERVICES_MODE,
  PRODUCTS_AND_SERVICES_REVIEW_ITEM,
} from '../../../Navigation/navigationConstants';
import AddOnProducts from './addOnProducts';
import ProductSetupComponent from './CoreProducts/ProductsSetup/productSetupComponent';
import GenericProductComponent from './genericProductComponent';
import ReviewProductsAndServices from './ProductsAndServicesReview/productsAndServicesReview';

/**
 * Generates the sidebar configuration for the Products & Services section.
 * This configures the navigation and dynamic rendering of product-related forms and review steps.
 *
 * @param changeRequest - The current organization details and change request context
 * @param formMethods - The react-hook-form methods for managing form state
 * @param navigateFn - Optional navigation function to switch sidebar sections/tabs
 * @returns SidebarConfig - The configuration object for the sidebar
 */
export const getProductsAndServicesSidebarConfig = (
  changeRequest: OrganizationDetails,
  formMethods: UseFormReturn<any>,
  navigateFn?: (section: string, tab: string) => void
): SidebarConfig => {
  // Helper to navigate to a specific sidebar item
  const navigateToProductsItem = (id: string, section?: string) =>
    navigateFn
      ? navigateFn(section || PRODUCTS_AND_SERVICES_MODE, id)
      : undefined;

  // Retrieve all plan designs from the form state
  const planDesigns = formMethods.watch(PLAN_DESIGNS_BASE_PATH) || [];

  // Will hold dynamically generated sidebar items for each product type
  let dynamicProductItems: any[] = [];

  // Only proceed if there are plan designs present
  if (planDesigns.length > 0) {
    // Use the first plan design to generate dynamic product items
    const planDesign = planDesigns[0] as PlanDesign;

    if (planDesign && planDesign.plan_design_ancillaries) {
      // Extract ancillaries for the plan design
      const planDesignAncillaries =
        planDesign.plan_design_ancillaries as PlanDesignAncillary[];

      if (planDesignAncillaries && planDesignAncillaries.length > 0) {
        // Group ancillaries by product type/category
        const productBuckets = organizePlanDesignAncillaries(
          planDesignAncillaries
        );

        // Generate config objects for each product bucket
        const productConfigs = generateProductConfigs(productBuckets);

        // Map each product config to a sidebar item with a custom component
        dynamicProductItems = productConfigs.map((config, index) => {
          return {
            id: config.tabId,
            label: config.subtitle,
            component: (
              <GenericProductComponent
                formMethods={formMethods}
                planDesignIndex={0}
                title={config.subtitle}
                description={`Manage ${config.productType} products for your plan design`}
                basePath={config.basePath}
                modalTitle={`Add ${config.productType} Product`}
                // Navigation for back/skip between dynamic product items
                onBack={() =>
                  navigateToProductsItem(
                    productConfigs[index - 1]?.tabId || ADD_ON_PRODUCTS_ITEM
                  )
                }
                onSkip={() =>
                  navigateToProductsItem(
                    productConfigs[index + 1]?.tabId ||
                      PRODUCTS_AND_SERVICES_REVIEW_ITEM
                  )
                }
                productType={config.productType}
                onUpdateActiveItem={navigateToProductsItem}
              />
            ),
          };
        });
      }
    }
  }

  // Return the sidebar configuration object
  return {
    sections: [
      {
        title: 'PRODUCTS & SERVICES',
        items: [
          {
            id: CORE_PRODUCTS_ITEM,
            label: 'Core Products',
            hasDropdown: true,
            dropdownItems: [
              {
                id: PRODUCT_SET_UP_ITEM,
                label: 'Product Set Up',
                component: (
                  <ProductSetupComponent
                    formMethods={formMethods}
                    onUpdateActiveItem={navigateToProductsItem}
                  />
                ),
              },
              {
                id: PHARMACY_NETWORK_ITEM,
                label: 'Pharmacy Network',
                component: (() => {
                  // Only show pharmacy network if features are present
                  if (!changeRequest?.features?.length) return null;

                  // Filter features for those related to Pharmacy Network
                  const pharmacyFeatures = changeRequest.features.filter(
                    (feature) =>
                      (feature.label || feature.name || '').includes(
                        'Pharmacy Network'
                      )
                  );

                  if (!pharmacyFeatures.length) return null;

                  // Generate unique tab IDs for each feature
                  const featureIds = pharmacyFeatures.map((f) =>
                    generateFeatureTabId(f)
                  );

                  // Render a feature component for each pharmacy feature
                  return (
                    <>
                      {pharmacyFeatures.map((feature, index) => (
                        <div
                          key={featureIds[index]}
                          className="pharmacy-feature-container"
                        >
                          {createFeatureComponent(
                            feature,
                            formMethods,
                            navigateToProductsItem,
                            // Next and previous navigation for features
                            index < pharmacyFeatures.length - 1
                              ? featureIds[index + 1]
                              : dynamicProductItems.length > 0
                              ? dynamicProductItems[0].id
                              : PRODUCTS_AND_SERVICES_REVIEW_ITEM,
                            index < pharmacyFeatures.length - 1
                              ? featureIds[index - 1]
                              : PRODUCT_SET_UP_ITEM
                          )}
                        </div>
                      ))}
                    </>
                  );
                })(),
              },
            ],
          },
          // Add-On Products section appears above dynamic product items
          {
            id: ADD_ON_PRODUCTS_ITEM,
            label: 'Add-On Products',
            component: (
              <AddOnProducts
                planId={changeRequest?.plan?.plan_id}
                formMethods={formMethods}
              />
            ),
          },
          // Insert dynamically generated product items here
          ...dynamicProductItems,
          {
            id: PRODUCTS_AND_SERVICES_REVIEW_ITEM,
            label: 'Review Products & Services',
            component: (
              <ReviewProductsAndServices
                formMethods={formMethods}
                onUpdateActiveItem={navigateToProductsItem}
                lastSectionId={dynamicProductItems.at(-1)?.id}
              />
            ),
          },
        ],
      },
    ],
  };
};
