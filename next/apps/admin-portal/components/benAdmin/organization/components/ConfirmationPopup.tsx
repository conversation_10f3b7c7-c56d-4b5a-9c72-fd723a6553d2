import {
  AlertDialog,
  AlertDialogBody,
  AlertDialogCloseButton,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogOverlay,
  Button,
} from '@chakra-ui/react';
import { FC, useRef } from 'react';

interface ConfirmationPopupProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  alertHeader: string;
  alertBody: string;
  confirmButtonText?: string;
  isConfirmLoading?: boolean;
  hideCancelButton?: boolean;
}

export const ConfirmationPopup: FC<ConfirmationPopupProps> = ({
  isOpen,
  onClose,
  onConfirm,
  alertHeader,
  alertBody,
  confirmButtonText = 'Delete',
  isConfirmLoading,
  hideCancelButton = false,
}) => {
  const cancelRef = useRef(null);

  const handleConfirm = () => {
    onConfirm();
  };

  return (
    <AlertDialog
      isOpen={isOpen}
      leastDestructiveRef={cancelRef}
      onClose={onClose}
      motionPreset="slideInBottom"
      closeOnOverlayClick={false}
      isCentered
    >
      <AlertDialogOverlay>
        <AlertDialogContent position="relative" maxWidth="lg">
          <AlertDialogHeader fontSize="lg" fontWeight="bold" paddingTop={5}>
            {alertHeader}
          </AlertDialogHeader>
          <AlertDialogCloseButton paddingTop={5} />

          <AlertDialogBody paddingTop={0}>{alertBody}</AlertDialogBody>

          <AlertDialogFooter>
            {!hideCancelButton && (
              <Button
                ref={cancelRef}
                onClick={onClose}
                ml={3}
                variant="outline"
                colorScheme="green"
              >
                Cancel
              </Button>
            )}
            <Button
              colorScheme="green"
              onClick={handleConfirm}
              ml={hideCancelButton ? 0 : 3}
              variant="solid"
              isLoading={isConfirmLoading}
            >
              {confirmButtonText}
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialogOverlay>
    </AlertDialog>
  );
};
