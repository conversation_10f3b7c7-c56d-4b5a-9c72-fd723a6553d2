import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { PicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistInterface';
import { formatDate } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Table/utils/tableUtils';
import {
  TemplateFieldConfig,
  TemplateFieldGroup,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import {
  UseFormHandleSubmit,
  UseFormRegister,
  UseFormReturn,
} from 'react-hook-form';

import { getPharmacyPath } from '../Config/pharmacyConfig';

/**
 * Generates form fields for in-house pharmacy information
 * Uses the centralized picklist maps hook to get all dropdown options
 */
export function getInHousePharmacyInformationFields(
  planData: Partial<OrganizationDetails>,
  handleSubmit: UseFormHandleSubmit<any>,
  register: UseFormRegister<any>,
  formMethods: UseFormReturn<any>,
  maps: Partial<PicklistMaps>
): TemplateFieldGroup[] {
  // Extract pharmacies from plan data, defaulting to empty array if not present
  const pharmacies = Array.isArray(planData?.plan?.pharmacies)
    ? planData.plan.pharmacies
    : [];

  // Map each pharmacy to a field group
  return pharmacies.map((pharmacy, index) => {
    return {
      subtitle: `In-House Pharmacy - ${pharmacy.name || ''}`,
      columns: 2,
      editable: true,
      handleSubmit,
      formMethods,
      register,
      fields: [
        {
          label: '340b',
          value: pharmacy?.is_340b,
          name: getPharmacyPath('is_340b', index),
          type: 'dropdownSelect',
          optionsMap: maps.yesNoMap,
        },
        {
          label: '340b Admin Fee',
          value: pharmacy?.admin_340b_fee,
          name: getPharmacyPath('admin_340b_fee', index),
        },
        {
          label: 'Custom Rates Notes',
          value: pharmacy?.custom_rate_notes,
          name: getPharmacyPath('custom_rate_notes', index),
        },
        {
          label: 'In House Pharmacy Status',
          value: pharmacy?.status_ind,
          name: getPharmacyPath('status_ind', index),
          type: 'dropdownSelect',
          optionsMap: maps.pharmacyStatusMap,
        },
        {
          label: 'Exclude from Financial Guarantee',
          value: pharmacy?.financial_guarantee_ind,
          name: getPharmacyPath('financial_guarantee_ind', index),
          type: 'dropdownSelect',
          optionsMap: maps.yesNoMap,
        },
        {
          label: 'Exclude from Rebate Protection',
          value: pharmacy?.rebate_protection_ind,
          name: getPharmacyPath('rebate_protection_ind', index),
          type: 'dropdownSelect',
          optionsMap: maps.yesNoMap,
        },
        {
          label: 'IHP Software',
          value: pharmacy?.ihp_software,
          name: getPharmacyPath('ihp_software', index),
        },
        {
          label: 'IHP Switch',
          value: pharmacy?.ihp_switch,
          name: getPharmacyPath('ihp_switch', index),
        },
        {
          label: 'Hardcoded Dispensing Fee Notes',
          value: pharmacy?.hardcoded_dispense_fee_notes,
          name: getPharmacyPath('hardcoded_dispense_fee_notes', index),
        },
        {
          label: 'Dispensing Fee Value',
          value: pharmacy?.dispensing_fee_value,
          name: getPharmacyPath('dispensing_fee_value', index),
        },
        {
          label: 'Hardcoded Dispensing Fee',
          value: pharmacy?.hardcoded_dispense_fee_ind,
          name: getPharmacyPath('hardcoded_dispense_fee_ind', index),
          type: 'dropdownSelect',
          optionsMap: maps.yesNoMap,
        },
        {
          label: 'In House Pharmacy Name',
          value: pharmacy?.legal_entity?.name,
          name: getPharmacyPath('name', index),
        },
        // Generate address fields dynamically based on array length
        ...(pharmacy?.legal_entity?.addresses || []).flatMap(
          (address, addrIndex) => [
            {
              label: `In House Pharmacy Address ${addrIndex + 1}`,
              value: `${address?.address_line_1 ?? ''} ${
                address?.address_line_2 ?? ''
              }`.trim(),
              name:
                addrIndex === 0
                  ? getPharmacyPath('address', index)
                  : `${getPharmacyPath('address', index)}_${addrIndex}`,
            },
            {
              label: `In House Pharmacy City ${addrIndex + 1}`,
              value: address?.city || '',
              name:
                addrIndex === 0
                  ? getPharmacyPath('city', index)
                  : `${getPharmacyPath('city', index)}_${addrIndex}`,
            },
            {
              label: `In House Pharmacy State ${addrIndex + 1}`,
              value: address?.state || '',
              name:
                addrIndex === 0
                  ? getPharmacyPath('state', index)
                  : `${getPharmacyPath('state', index)}_${addrIndex}`,
              type: 'dropdownSelect',
              optionsMap: maps.stateMap,
            },
            {
              label: `In House Pharmacy Zip ${addrIndex + 1}`,
              value: address?.postal_code || '',
              name:
                addrIndex === 0
                  ? getPharmacyPath('postal_code', index)
                  : `${getPharmacyPath('postal_code', index)}_${addrIndex}`,
            },
          ]
        ),
        {
          label: 'In House Pharmacy Contact',
          value: `${
            pharmacy?.legal_entity?.person_assignments?.[0]?.person
              ?.first_name ?? ''
          } ${
            pharmacy?.legal_entity?.person_assignments?.[0]?.person
              ?.last_name ?? ''
          }`.trim(),
          name: getPharmacyPath('person', index),
        },
        // Generate phone fields dynamically based on array length
        ...(pharmacy?.legal_entity?.phones || []).flatMap(
          (phone, phoneIndex) => [
            {
              label: `In House Pharmacy Phone ${phoneIndex + 1}`,
              value: phone?.phone_number || '',
              name:
                phoneIndex === 0
                  ? getPharmacyPath('phone_number', index)
                  : `${getPharmacyPath('phone_number', index)}_${phoneIndex}`,
            },
          ]
        ),
        {
          label: 'In House Pharmacy Record',
          value: pharmacy?.pharmacy_id,
          name: getPharmacyPath('pharmacy_id', index),
        },
        {
          label: 'In House Pharmacy Billing',
          value: pharmacy?.billing_ind || '',
          name: getPharmacyPath('billing_ind', index),
          type: 'dropdownSelect',
          optionsMap: maps.pharmacyBillingMap,
        },
        {
          label: 'In House Pharmacy Expiration Date',
          value: formatDate(pharmacy?.expiration_date),
          name: getPharmacyPath('expiration_date', index),
        },
        {
          label: 'In House Pharmacy Effective Date',
          value: formatDate(pharmacy?.effective_date),
          name: getPharmacyPath('effective_date', index),
        },
        {
          label: 'In House Pharmacy Notes',
          value: pharmacy?.notes,
          name: getPharmacyPath('notes', index),
        },
        {
          label: 'In House Pharmacy Reimbursement Setup',
          value: pharmacy?.pricing_setup_ind,
          name: getPharmacyPath('pricing_setup_ind', index),
          type: 'dropdownSelect',
          optionsMap: maps.pharmacyPricingMap,
        },
        {
          label: 'Multi-Ingredient Compounds Processed',
          value: pharmacy?.multi_ingredient_compounds_ind,
          name: getPharmacyPath('multi_ingredient_compounds_ind', index),
          type: 'dropdownSelect',
          optionsMap: maps.yesNoMap,
        },
        {
          label: 'NABP Number',
          value: pharmacy?.nabp_number,
          name: getPharmacyPath('nabp_number', index),
        },
        {
          label: 'NPI Number',
          value: pharmacy?.npi_number,
          name: getPharmacyPath('npi_number', index),
        },
        {
          label: 'Pharmacy Name',
          value: pharmacy?.name,
          name: getPharmacyPath('name', index),
        },
        {
          label: 'Pharmacy Ownership',
          value: pharmacy?.pharmacy_ownership_ind,
          name: getPharmacyPath('pharmacy_ownership_ind', index),
          type: 'dropdownSelect',
          optionsMap: maps.pharmacyOwnershipMap,
        },
      ] as Partial<TemplateFieldConfig>[],
    };
  });
}
