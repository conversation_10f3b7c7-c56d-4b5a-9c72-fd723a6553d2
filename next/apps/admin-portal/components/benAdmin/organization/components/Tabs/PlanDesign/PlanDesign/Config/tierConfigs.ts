// tierFieldsConfig.ts
import { useMemo } from 'react';

// tierPaths.ts
export const TIER_FIELD_NAMES = {
  name: 'name',
  effective_date: 'effective_date',
  expiration_date: 'expiration_date',
  tier_ind: 'tier_ind',
  drug_list_ind: 'drug_list_ind',
  pharmacy_channel_ind: 'pharmacy_channel_ind',
  days_supply: 'days_supply',
  copay_coinsurance_amt: 'copay_coinsurance_amt',
  cap_amt: 'cap_amt',
};

// useTierTableColumns.ts

export const getTierTableColumns = () => {
  return [
    {
      key: 'pharmacy_channel_ind',
      override: {
        headerName: 'Pharmacy Channel',
        dataType: 'text',
      },
    },
    {
      key: 'days_supply',
      override: {
        headerName: 'Actual Day Supply',
        formatter: (value: any) => {
          return value != null ? value : '-';
        },
      },
    },
    {
      key: 'copay_coinsurance_amt',
      override: {
        style: { textAlign: 'right' },
        headerName: 'Copay/Coinsurance Amt.',
        datatype: 'text',
        data: (row: any) => ({
          value: row.copay_coinsurance_amt,
          display:
            row.copay_coinsurance_amt !== null
              ? row.datatype === 'dollar'
                ? `$${Number(row.copay_coinsurance_amt).toFixed(2)}`
                : row.datatype === 'percent'
                ? `${Number(row.copay_coinsurance_amt).toFixed(2)}%${
                    row.co_insurance_ltgt_ind !== null
                      ? ` ${
                          row.co_insurance_ltgt_ind === '0'
                            ? 'Lesser'
                            : 'Greater'
                        }`
                      : ''
                  }`
                : row?.copay_coinsurance_amt?.toString()
              : '-',
        }),
      },
    },
    {
      key: 'co_insurance_min_amount',
      override: {
        headerName: 'Min. Amount',
        datatype: 'dollar',
        data: (row: any) => ({
          value: row.co_insurance_min_amount,
          display: row.co_insurance_min_amount,
        }),
      },
    },
    {
      key: 'co_insurance_max_amount',
      override: {
        headerName: 'Max. Amount',
        datatype: 'dollar',
        data: (row: any) => ({
          value: row.co_insurance_max_amount,
          display: row.co_insurance_max_amount,
        }),
      },
    },
    {
      key: 'effective_date',
      override: {
        headerName: 'Tier Effective Date',
        datatype: 'Date',
      },
    },
    {
      key: 'expiration_date',
      override: {
        headerName: 'Tier Expiration Date',
        datatype: 'Date',
      },
    },
    {
      key: 'drug_list_ind',
      override: {
        headerName: 'Drug List',
        datatype: 'text',
      },
    },
  ];
};

export const useTierTableColumns = () => {
  return useMemo(() => getTierTableColumns(), []);
};
