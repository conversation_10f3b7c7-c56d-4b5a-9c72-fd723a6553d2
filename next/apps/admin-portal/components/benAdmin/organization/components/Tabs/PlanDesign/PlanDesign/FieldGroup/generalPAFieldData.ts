import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { PicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistInterface';
import { TemplateFieldGroup } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import {
  UseFormHandleSubmit,
  UseFormRegister,
  UseFormReturn,
} from 'react-hook-form';

import { getGeneralPAPaths } from '../Config/generalPAConfig';
import { accumTransferMap, medicalIntMap } from './maps';

export function getGeneralPAFields(
  planData: Partial<OrganizationDetails>,
  selectedIndex: number,
  handleSubmit: UseFormHandleSubmit<any>,
  register: UseFormRegister<any>,
  formMethods: UseFormReturn<any>,
  maps: Partial<PicklistMaps>
): TemplateFieldGroup[] {
  const planDesigns = Array.isArray(planData?.plan?.plan_designs)
    ? planData.plan.plan_designs
    : [];

  // Get the plan design corresponding to the selected index
  const planDesign = planDesigns[selectedIndex];
  if (!planDesign) {
    return [];
  }

  const planDesignDetails = planDesign?.plan_design_details || [];
  const planDesignConfig = getGeneralPAPaths(selectedIndex);

  // Use map with early return pattern, then filter out null values
  return planDesignDetails
    .map((designDetail) => {
      return {
        subtitle: `Pharmacy Accumulator - General - ${planDesign?.name || ''}`,
        columns: 2,
        editable: true,
        handleSubmit,
        formMethods,
        register,
        fields: [
          {
            label: 'Accum Transfer',
            value: designDetail?.accum_transfer_ind,
            name: planDesignConfig.accum_transfer_ind,
            type: 'dropdownSelect',
            optionsMap: accumTransferMap,
          },
          {
            label: 'Accumulators Integrated with Medical',
            value: designDetail?.accum_integrated_ind,
            name: planDesignConfig.accum_integrated_ind,
            type: 'dropdownSelect',
            optionsMap: maps.yesNoMap,
          },
          {
            label: 'Plan Max Coverage Amount',
            value: designDetail?.max_coverage_amount,
            name: planDesignConfig.max_coverage_amount,
            type: 'input',
            placeholder: 'Enter Plan Max Coverage Amount',
          },
          {
            label: 'Medical Integration Tier',
            value: designDetail?.medical_integration_tier_ind,
            name: planDesignConfig.medical_integration_tier_ind,
            type: 'dropdownSelect',
            optionsMap: medicalIntMap,
          },
        ],
      };
    })
    .filter(Boolean) as TemplateFieldGroup[]; // Remove null values and assert type
}
