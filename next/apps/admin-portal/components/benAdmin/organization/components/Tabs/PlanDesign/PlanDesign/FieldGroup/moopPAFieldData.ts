import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { PicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistInterface';
import { TemplateFieldGroup } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import {
  UseFormHandleSubmit,
  UseFormRegister,
  UseFormReturn,
} from 'react-hook-form';

import { getMoopPAPaths } from '../Config/moopPAConfig';
import {
  accumPeriodMap,
  benefitPeriodMap,
  carryOverMap,
  drugListMap,
  drugTypeMap,
  embeddedMap,
  excludeDrugListMap,
  formularyMap,
  integratedMap,
  penaltiesApplyMap,
  pharmacyChannelMap,
  sharedMap,
} from './maps';

export function getMoopPAFields(
  planData: Partial<OrganizationDetails>,
  selectedIndex: number,
  handleSubmit: UseFormHandleSubmit<any>,
  register: UseFormRegister<any>,
  formMethods: UseFormReturn<any>,
  maps: Partial<PicklistMaps>
): TemplateFieldGroup[] {
  const planDesigns = Array.isArray(planData?.plan?.plan_designs)
    ? planData.plan.plan_designs
    : [];

  // Get the plan design corresponding to the selected index
  const planDesign = planDesigns[selectedIndex];
  if (!planDesign) {
    return [];
  }

  const planDesignDetails = planDesign?.plan_design_details || [];
  const planDesignConfig = getMoopPAPaths(selectedIndex);

  // Use map with early return pattern, then filter out null values
  return planDesignDetails
    .map((designDetail) => {
      const planDesignDestailsAccumMoop = designDetail?.accumulation_moop?.[0];

      // Skip plans without accumulation_deductible details by returning null
      if (!planDesignDestailsAccumMoop) {
        return null;
      }

      return {
        subtitle: `Pharmacy Accumulator - Maximum Out of Pocket - ${
          planDesign?.name || ''
        }`,
        columns: 2,
        editable: true,
        handleSubmit,
        formMethods,
        register,
        fields: [
          {
            label: 'Does Maximum Out of Pocket Apply?',
            value: planDesignDestailsAccumMoop?.apply_ind,
            name: planDesignConfig.apply_ind,
            type: 'dropdownSelect',
            optionsMap: maps.yesNoMap,
          },
          {
            label: 'Accums Tier Name',
            value: planDesignDestailsAccumMoop?.accums_tier_name,
            name: planDesignConfig.accums_tier_name,
            type: 'input',
            placeholder: 'Enter Accums Tier Name',
          },
          {
            label: 'Accums Tier Effective Date',
            value: planDesignDestailsAccumMoop?.effective_date,
            name: planDesignConfig.effective_date,
            type: 'datepicker',
          },
          {
            label: 'Accums Tier End Date',
            value: planDesignDestailsAccumMoop?.expiration_date,
            name: planDesignConfig.expiration_date,
            type: 'datepicker',
          },
          {
            label: 'Accums Tier PBC order',
            value: planDesignDestailsAccumMoop?.pbc_order,
            name: planDesignConfig.pbc_order,
            type: 'input',
            placeholder: 'Enter Accums Tier PBC order',
          },
          {
            label: 'Maximum Out of Pocket Accumulation Period',
            value: planDesignDestailsAccumMoop?.accum_period_ind,
            name: planDesignConfig.accum_period_ind,
            type: 'dropdownSelect',
            optionsMap: accumPeriodMap,
          },
          {
            label: 'Specify Maximum Out of Pocket Accumulation Period',
            value: planDesignDestailsAccumMoop?.specify_accum_period_ind,
            name: planDesignConfig.specify_accum_period_ind,
            type: 'dropdownSelect',
            optionsMap: maps.benefitPeriodsMap,
          },
          {
            label: 'Benefit Period Length',
            value: planDesignDestailsAccumMoop?.benefit_period_length_ind,
            name: planDesignConfig.benefit_period_length_ind,
            type: 'dropdownSelect',
            optionsMap: benefitPeriodMap,
          },
          {
            label: 'Benefit Period Length - Other',
            value: planDesignDestailsAccumMoop?.benefit_period_length_other,
            name: planDesignConfig.benefit_period_length_other,
            type: 'input',
            placeholder: 'Enter Benefit Period Length - Other',
          },
          {
            label: 'Do Priming Balances Apply?',
            value: planDesignDestailsAccumMoop?.priming_balances_ind,
            name: planDesignConfig.priming_balances_ind,
            type: 'dropdownSelect',
            optionsMap: maps.yesNoMap,
          },
          {
            label: 'Carryover Phase',
            value: planDesignDestailsAccumMoop?.carryover_phase_ind,
            name: planDesignConfig.carryover_phase_ind,
            type: 'dropdownSelect',
            optionsMap: carryOverMap,
          },
          {
            label: 'Describe Carryover Phase',
            value: planDesignDestailsAccumMoop?.describe_carryover_phase,
            name: planDesignConfig.describe_carryover_phase,
            type: 'input',
            placeholder: 'Enter Describe Carryover Phase',
          },
          {
            label: 'Maximum Out of Pocket Integrated?',
            value: planDesignDestailsAccumMoop?.integrated_ind,
            name: planDesignConfig.integrated_ind,
            type: 'dropdownSelect',
            optionsMap: integratedMap,
          },
          {
            label: 'Maximum Out of Pocket Embedded?',
            value: planDesignDestailsAccumMoop?.embedded_ind,
            name: planDesignConfig.embedded_ind,
            type: 'dropdownSelect',
            optionsMap: embeddedMap,
          },
          {
            label: 'Individual Maximum Out of Pocket Amount',
            value: planDesignDestailsAccumMoop?.individual_plan_amount,
            name: planDesignConfig.individual_plan_amount,
            type: 'input',
            placeholder: 'Enter Individual Maximum Out of Pocket Amount',
          },
          {
            label: 'Family Maximum Out of Pocket Amount',
            value: planDesignDestailsAccumMoop?.family_plan_amount,
            name: planDesignConfig.family_plan_amount,
            type: 'input',
            placeholder: 'Enter Family Maximum Out of Pocket Amount',
          },
          {
            label: 'Employee +1 Dep Maximum Out of Pocket Amount',
            value: planDesignDestailsAccumMoop?.employee_1_dep_amount,
            name: planDesignConfig.employee_1_dep_amount,
            type: 'input',
            placeholder: 'Enter Employee +1 Dep Maximum Out of Pocket Amount',
          },
          {
            label: 'Individual Maximum Out of Pocket within Family Amount',
            value: planDesignDestailsAccumMoop?.individual_within_family_amount,
            name: planDesignConfig.individual_within_family_amount,
            type: 'input',
            placeholder:
              'Enter Individual Maximum Out of Pocket within Family Amount',
          },
          {
            label: 'Maximum Out of Pocket Applies to Retail, Mail & Paper',
            value: planDesignDestailsAccumMoop?.apply_retail_mail_paper_ind,
            name: planDesignConfig.apply_retail_mail_paper_ind,
            type: 'dropdownSelect',
            optionsMap: maps.yesNoMap,
          },
          {
            label: 'Penalties Apply to Maximum Out of Pocket?',
            value: planDesignDestailsAccumMoop?.penalties_apply_ind,
            name: planDesignConfig.penalties_apply_ind,
            type: 'dropdownSelect',
            optionsMap: penaltiesApplyMap,
          },
          {
            label: 'Penalties Apply After Maximum Out of Pocket?',
            value: planDesignDestailsAccumMoop?.penalties_apply_after_ind,
            name: planDesignConfig.penalties_apply_after_ind,
            type: 'dropdownSelect',
            optionsMap: penaltiesApplyMap,
          },
          {
            label: 'Shared Indicator',
            value: planDesignDestailsAccumMoop?.shared_ind,
            name: planDesignConfig.shared_ind,
            type: 'dropdownSelect',
            optionsMap: sharedMap,
          },
          {
            label: 'Drug Type Status',
            value: planDesignDestailsAccumMoop?.drug_type_status_ind,
            name: planDesignConfig.drug_type_status_ind,
            type: 'dropdownSelect',
            optionsMap: drugTypeMap,
          },
          {
            label: 'Formulary Status',
            value: planDesignDestailsAccumMoop?.formulary_status_ind,
            name: planDesignConfig.formulary_status_ind,
            type: 'dropdownSelect',
            optionsMap: formularyMap,
          },
          {
            label: 'work Status',
            value: planDesignDestailsAccumMoop?.network_status_ind,
            name: planDesignConfig.network_status_ind,
            type: 'dropdownSelect',
            optionsMap: maps.networkApplicabilityMap,
          },
          {
            label: 'Pharmacy Channel',
            value: planDesignDestailsAccumMoop?.pharmacy_channel_ind,
            name: planDesignConfig.pharmacy_channel_ind,
            type: 'dropdownSelect',
            optionsMap: pharmacyChannelMap,
          },
          {
            label: 'Include Drug List',
            value: planDesignDestailsAccumMoop?.include_drug_list_ind,
            name: planDesignConfig.include_drug_list_ind,
            type: 'dropdownSelect',
            optionsMap: drugListMap,
          },
          {
            label: 'Exclude Drug List',
            value: planDesignDestailsAccumMoop?.exclude_drug_list_ind,
            name: planDesignConfig.exclude_drug_list_ind,
            type: 'dropdownSelect',
            optionsMap: excludeDrugListMap,
          },
          {
            label: 'Maximum Out of Pocket Notes',
            value: planDesignDestailsAccumMoop?.notes,
            name: planDesignConfig.notes,
            type: 'input',
            placeholder: 'Enter Maximum Out of Pocket Notes',
          },
        ],
      };
    })
    .filter(Boolean) as TemplateFieldGroup[]; // Remove null values and assert type
}
