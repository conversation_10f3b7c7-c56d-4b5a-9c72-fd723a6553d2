import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { PicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistInterface';
import { TemplateFieldGroup } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import {
  UseFormHandleSubmit,
  UseFormRegister,
  UseFormReturn,
} from 'react-hook-form';

import { getOtherPAPath } from '../Config/otherPAConfig';
import {
  accumPeriodMap,
  accumTierMap,
  benefitPeriodMap,
  carryOverMap,
  drugListMap,
  drugTypeMap,
  excludeDrugListMap,
  formularyMap,
  pharmacyChannelMap,
  sharedMap,
} from './maps';

export function getOtherPAFields(
  planData: Partial<OrganizationDetails>,
  selectedIndex: number,
  handleSubmit: UseFormHandleSubmit<any>,
  register: UseFormRegister<any>,
  formMethods: UseFormReturn<any>,
  maps: Partial<PicklistMaps>
): TemplateFieldGroup[] {
  const planDesigns = Array.isArray(planData?.plan?.plan_designs)
    ? planData.plan.plan_designs
    : [];

  // Get the plan design corresponding to the selected index
  const planDesign = planDesigns[selectedIndex];
  if (!planDesign) {
    return [];
  }

  const planDesignDetails = planDesign?.plan_design_details || [];

  const otherCapTemplates: TemplateFieldGroup[] = [];

  // Use map with early return pattern, then filter out null values
  planDesignDetails.forEach((designDetail) => {
    const planDesignDetailsAccumOther = designDetail?.accumulation_other;

    // Skip plans without accumulation_other details by returning null
    if (!planDesignDetailsAccumOther) {
      return null;
    }

    planDesignDetailsAccumOther.forEach((otherCap, index) => {
      otherCapTemplates.push({
        subtitle: `Pharmacy Accumulator - Other Cap - ${
          otherCap.accums_tier_name || ''
        }`,
        columns: 2,
        editable: true,
        handleSubmit,
        formMethods,
        register,
        fields: [
          {
            label: 'Accums Tier Type',
            value: otherCap.other_accum_type_ind,
            name: getOtherPAPath('other_accum_type_ind', index),
            type: 'dropdownSelect',
            optionsMap: accumTierMap,
          },
          {
            label: 'Accums Tier Name',
            value: otherCap?.accums_tier_name,
            name: getOtherPAPath('accums_tier_name', index),
            type: 'input',
            placeholder: 'Enter Accums Tier Name',
          },
          {
            label: 'Accums Tier Effective Date',
            value: otherCap?.effective_date,
            name: getOtherPAPath('effective_date', index),
            type: 'datepicker',
          },
          {
            label: 'Accums Tier End Date',
            value: otherCap?.expiration_date,
            name: getOtherPAPath('expiration_date', index),
            type: 'datepicker',
          },
          {
            label: 'Accums Tier PBC order',
            value: otherCap?.pbc_order,
            name: getOtherPAPath('pbc_order', index),
            type: 'input',
            placeholder: 'Enter Accums Tier PBC order',
          },
          {
            label: 'Accumulation Period',
            value: otherCap?.accum_period_ind,
            name: getOtherPAPath('accum_period_ind', index),
            type: 'dropdownSelect',
            optionsMap: accumPeriodMap,
          },
          {
            label: 'Specify Other Cap Accumulation Period',
            value: otherCap?.specify_accum_period_ind,
            name: getOtherPAPath('specify_accum_period_ind', index),
            type: 'dropdownSelect',
            optionsMap: maps.benefitPeriodsMap,
          },
          {
            label: 'Benefit Period Length',
            value: otherCap?.benefit_period_length_ind,
            name: getOtherPAPath('benefit_period_length_ind', index),
            type: 'dropdownSelect',
            optionsMap: benefitPeriodMap,
          },
          {
            label: 'Benefit Period Length - Other',
            value: otherCap?.benefit_period_length_other,
            name: getOtherPAPath('benefit_period_length_other', index),
            type: 'input',
            placeholder: 'Enter Benefit Period Length - Other',
          },
          {
            label: 'Do Priming Balances Apply?',
            value: otherCap?.priming_balances_ind,
            name: getOtherPAPath('priming_balances_ind', index),
            type: 'dropdownSelect',
            optionsMap: maps.yesNoMap,
          },
          {
            label: 'Carryover Phase',
            value: otherCap?.carryover_phase_ind,
            name: getOtherPAPath('pbc_order', index),
            type: 'dropdownSelect',
            optionsMap: carryOverMap,
          },
          {
            label: 'Describe Carryover Phase',
            value: otherCap?.describe_carryover_phase,
            name: getOtherPAPath('describe_carryover_phase', index),
            type: 'input',
            placeholder: 'Enter Describe Carryover Phase',
          },
          {
            label: 'Individual Cap Amount',
            value: otherCap?.individual_plan_amount,
            name: getOtherPAPath('individual_plan_amount', index),
            type: 'input',
            placeholder: 'Enter Individual Cap Amount',
          },
          {
            label: 'Family Cap Amount',
            value: otherCap?.family_plan_amount,
            name: getOtherPAPath('family_plan_amount', index),
            type: 'input',
            placeholder: 'Enter Family Cap Amount',
          },
          {
            label: 'Employee +1 Cap Amount',
            value: otherCap?.employee_1_dep_amount,
            name: getOtherPAPath('employee_1_dep_amount', index),
            type: 'input',
            placeholder: 'Enter Employee +1 Cap Amount',
          },
          {
            label: 'Individual within Family Cap Amount',
            value: otherCap?.individual_within_family_amount,
            name: getOtherPAPath('individual_within_family_amount', index),
            type: 'input',
            placeholder: 'Enter Individual within Family Cap Amount',
          },
          {
            label: 'Max Allowable Cap',
            value: otherCap?.max_allowable_cap,
            name: getOtherPAPath('max_allowable_cap', index),
            type: 'input',
            placeholder: 'Enter Max Allowable Cap',
          },
          {
            label: 'CDH Class Code',
            value: otherCap?.cdh_class_code_ind,
            name: getOtherPAPath('cdh_class_code_ind', index),
            type: 'dropdownSelect',
            optionsMap: maps.cdhClassCodeMap,
          },
          {
            label: 'Shared Indicator',
            value: otherCap?.shared_ind,
            name: getOtherPAPath('shared_ind', index),
            type: 'dropdownSelect',
            optionsMap: sharedMap,
          },
          {
            label: 'Drug Type Status',
            value: otherCap?.drug_type_status_ind,
            name: getOtherPAPath('drug_type_status_ind', index),
            type: 'dropdownSelect',
            optionsMap: drugTypeMap,
          },
          {
            label: 'Formulary Status',
            value: otherCap?.formulary_status_ind,
            name: getOtherPAPath('formulary_status_ind', index),
            type: 'dropdownSelect',
            optionsMap: formularyMap,
          },
          {
            label: 'Network Status',
            value: otherCap?.network_status_ind,
            name: getOtherPAPath('network_status_ind', index),
            type: 'dropdownSelect',
            optionsMap: maps.networkApplicabilityMap,
          },
          {
            label: 'Pharmacy Channel',
            value: otherCap?.pharmacy_channel_ind,
            name: getOtherPAPath('pharmacy_channel_ind', index),
            type: 'dropdownSelect',
            optionsMap: pharmacyChannelMap,
          },
          {
            label: 'Include Drug List',
            value: otherCap?.include_drug_list_ind,
            name: getOtherPAPath('include_drug_list_ind', index),
            type: 'dropdownSelect',
            optionsMap: drugListMap,
          },
          {
            label: 'Exclude Drug List',
            value: otherCap?.exclude_drug_list_ind,
            name: getOtherPAPath('exclude_drug_list_ind', index),
            type: 'dropdownSelect',
            optionsMap: excludeDrugListMap,
          },
          {
            label: 'Other Cap Notes',
            value: otherCap?.notes,
            name: getOtherPAPath('notes', index),
            type: 'input',
            placeholder: 'Enter Other Cap Notes',
          },
        ],
      });
    });
  });
  return otherCapTemplates.filter(Boolean) as TemplateFieldGroup[];
}
