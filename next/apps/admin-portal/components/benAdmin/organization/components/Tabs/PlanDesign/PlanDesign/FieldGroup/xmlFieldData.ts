import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { PicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistInterface';
import { TemplateFieldGroup } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import {
  UseFormHandleSubmit,
  UseFormRegister,
  UseFormReturn,
} from 'react-hook-form';

import { getXmlPaths } from '../Config/xmlConfig';
import { accumsMap, hraMemberMap, networkMap, spendingAccMap } from './maps';

export function getXmlFields(
  planData: Partial<OrganizationDetails>,
  selectedIndex: number,
  handleSubmit: UseFormHandleSubmit<any>,
  register: UseFormRegister<any>,
  formMethods: UseFormReturn<any>,
  maps: Partial<PicklistMaps>
): TemplateFieldGroup[] {
  // Early return if no plan designs available
  const planDesigns = Array.isArray(planData?.plan?.plan_designs)
    ? planData.plan.plan_designs
    : [];

  // Get the plan design corresponding to the selected index
  const planDesign = planDesigns[selectedIndex];
  if (!planDesign) {
    return [];
  }

  const planDesignDetails = planDesign?.plan_design_details || [];
  const planDesignConfig = getXmlPaths(selectedIndex);

  // Map through each detail and create field configurations
  return planDesignDetails
    .map((designDetail) => {
      // Get the first ESI detail (if available)
      const esiDetail = designDetail?.plan_design_detail_esi?.[0];

      // Skip plans without ESI details
      if (!esiDetail) {
        return null;
      }

      // Return the field configurations for this detail
      return {
        subtitle: `XML Leave Behind - Info Provided by ESI - ${
          planDesign?.name || ''
        }`,
        columns: 2,
        editable: true,
        handleSubmit,
        formMethods,
        register,
        fields: [
          {
            label: 'Shared Vendor ID',
            value: esiDetail.shared_vendor_id,
            name: planDesignConfig.shared_vendor_id,
            type: 'input',
            placeholder: 'Enter Shared Vendor ID',
          },
          {
            label: 'Network Applicability',
            value: esiDetail.network_applicability_ind,
            name: planDesignConfig.network_applicability_ind,
            type: 'dropdownSelect',
            optionsMap: networkMap,
          },
          {
            label: 'Vendor Policy Number',
            value: esiDetail.vendor_policy_number,
            name: planDesignConfig.vendor_policy_number,
            type: 'input',
            placeholder: 'Enter Vendor Policy Number',
          },
          {
            label: 'Spending Account Type',
            value: esiDetail.spending_account_type_ind,
            name: planDesignConfig.spending_account_type_ind,
            type: 'dropdownSelect',
            optionsMap: spendingAccMap,
          },
          {
            label: 'XML Accums Complete',
            value: esiDetail.xml_accums_complete_ind,
            name: planDesignConfig.xml_accums_complete_ind,
            type: 'dropdownSelect',
            optionsMap: accumsMap,
          },
          {
            label: 'HRA Members Access',
            value: esiDetail.hra_members_access_ind,
            name: planDesignConfig.hra_members_access_ind,
            type: 'dropdownSelect',
            optionsMap: hraMemberMap,
          },
          {
            label: 'HSA Admin Phone Number',
            value: esiDetail.hsa_admin_phone,
            name: planDesignConfig.hsa_admin_phone,
            type: 'input',
            placeholder: 'Enter HSA Admin Phone Number',
          },
          {
            label: 'HSA Medical Phone Number',
            value: esiDetail.hsa_medical_phone,
            name: planDesignConfig.hsa_medical_phone,
            type: 'input',
            placeholder: 'Enter HSA Medical Phone Number',
          },
        ],
      };
    })
    .filter(Boolean) as TemplateFieldGroup[]; // Remove null entries
}
