import { PlanDesignAncillary } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { PicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistInterface';
import {
  TemplateFieldConfig,
  TemplateFieldGroup,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import { UseFormHandleSubmit, UseFormReturn } from 'react-hook-form';

import {
  getProductPath,
  PRODUCT_FIELD_LABELS,
  PRODUCT_FIELD_PATHS,
  PRODUCT_FIELD_TYPES,
} from './productConfig';

/**
 * createProductFieldsGenerator - Creates a function that generates product fields
 * with standardized labels for any product type
 *
 * @param productType - Type of the product from plan_ancillary
 * @param subtitle - Subtitle for the field group
 * @param planDesignIndex - Index of the plan design
 * @param handleSubmit - Form submission handler
 * @param formMethods - Form methods
 * @param maps - Picklist maps
 * @returns A function that generates product fields
 */
export const createProductFieldsGenerator = (
  productType: string,
  subtitle: string,
  planDesignIndex: number,
  handleSubmit: UseFormHandleSubmit<any>,
  formMethods: UseFormReturn<any>,
  maps: Partial<PicklistMaps>
) => {
  /**
   * Generated function that creates product fields for all products
   *
   * @param ancillaryEntries - Array of { ancillary, ancillaryIndex } objects
   * @returns Array of standardized field configs for all products
   */
  return (
    ancillaryEntries: {
      ancillary: PlanDesignAncillary;
      ancillaryIndex: number;
    }[]
  ): TemplateFieldGroup[] => {
    // Create an array to hold all product field groups
    const fieldGroups: TemplateFieldGroup[] = [];

    // Loop through all ancillary entries of the specified product type
    ancillaryEntries.forEach(({ ancillary, ancillaryIndex }, idx) => {
      if (ancillary.product_type !== productType) {
        return;
      }

      // Create field configs for current ancillary using standardized paths and labels
      const productFields: Partial<TemplateFieldConfig>[] = Object.keys(
        PRODUCT_FIELD_PATHS
      ).map((fieldKey) => {
        const fieldType =
          PRODUCT_FIELD_TYPES[fieldKey as keyof typeof PRODUCT_FIELD_TYPES];
        const name = getProductPath(
          planDesignIndex,
          ancillaryIndex,
          fieldKey as keyof typeof PRODUCT_FIELD_PATHS
        );
        let value;
        let label =
          PRODUCT_FIELD_LABELS[fieldKey as keyof typeof PRODUCT_FIELD_LABELS];
        // Special logic for Product Name/Product Option field
        if (fieldKey === 'name') {
          if (
            ancillary.product_options !== undefined &&
            ancillary.product_options !== null &&
            ancillary.product_options !== ''
          ) {
            label = 'Product Option';
            value = ancillary.product_options;
          } else {
            label = 'Product Name';
            value = ancillary.product?.name;
          }
        } else {
          // Get value from form state using formMethods for all other fields
          value = formMethods.getValues(name);
        }
        const baseConfig = {
          label,
          value,
          name,
          type: fieldType,
        };

        // Only add optionsMap for specific fields
        if (
          fieldKey === 'eligibilityExports' ||
          fieldKey === 'pharmacyClaimsExport'
        ) {
          return {
            ...baseConfig,
            optionsMap: maps.yesNoMap,
          } as Partial<TemplateFieldConfig>;
        }
        return baseConfig as Partial<TemplateFieldConfig>;
      });

      // Omit group if all non-name/non-option fields are null/empty/undefined
      const hasNonNullField = productFields.some((field) => {
        if (!field) return false;
        // Only check fields that are not Product Name or Product Option
        if (
          field.label === 'Product Name' ||
          field.label === 'Product Option'
        ) {
          return false;
        }
        return (
          field.value !== null &&
          field.value !== undefined &&
          field.value !== ''
        );
      });
      if (!hasNonNullField) {
        return;
      }

      // Create a field group for this ancillary
      let groupSubtitle = subtitle;
      // If product_options is shown, add product name to subtitle
      if (
        ancillary.product_options !== undefined &&
        ancillary.product_options !== null &&
        ancillary.product_options !== ''
      ) {
        if (ancillary.product?.name) {
          groupSubtitle = `${subtitle} - ${ancillary.product.name}`;
        }
      }
      const fieldGroup: TemplateFieldGroup = {
        subtitle: groupSubtitle,
        columns: 2,
        editable: true,
        handleSubmit,
        formMethods,
        fields: productFields,
        register: formMethods.register,
      };
      // Add this field group to our array
      fieldGroups.push(fieldGroup);
    });
    return fieldGroups;
  };
};
