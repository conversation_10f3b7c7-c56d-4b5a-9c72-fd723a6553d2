import { PLAN_DESIGNS_BASE_PATH } from 'apps/admin-portal/components/benAdmin';
import { PlanDesignAncillary } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { getBasePath } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';

export interface ProductBucketItem {
  planDesignIndex: number;
  ancillaryIndex: number;
  ancillary: PlanDesignAncillary;
}

export interface ProductBuckets {
  [key: string]: ProductBucketItem[];
}

export interface ProductConfig {
  productType: string;
  tabId: string;
  subtitle: string;
  basePath: string;
}

/**
 * Organizes plan design ancillaries into buckets by product type
 */
export const organizePlanDesignAncillaries = (
  planDesignAncillaries: PlanDesignAncillary[] = []
): ProductBuckets => {
  const buckets: ProductBuckets = {};

  // Early return if no ancillaries provided
  if (!planDesignAncillaries) {
    console.log('No plan design ancillaries provided');
    return buckets;
  }

  // Ensure we have an array to work with
  let ancillariesArray: PlanDesignAncillary[] = [];

  if (Array.isArray(planDesignAncillaries)) {
    ancillariesArray = planDesignAncillaries;
  } else if (typeof planDesignAncillaries === 'object') {
    // Handle case where it might be an object instead of array
    ancillariesArray = Object.values(planDesignAncillaries);
  } else {
    console.log(
      'Invalid planDesignAncillaries format:',
      typeof planDesignAncillaries
    );
    return buckets;
  }

  ancillariesArray.forEach(
    (ancillary: PlanDesignAncillary, ancillaryIndex: number) => {
      if (!ancillary) {
        console.log(
          `Skipping null/undefined ancillary at index ${ancillaryIndex}`
        );
        return;
      }

      // Get product type from the ancillary
      const productType =
        ancillary.product_type ||
        ancillary.product?.product_class?.name ||
        'Unknown Product Type';

      // Initialize bucket if it doesn't exist
      if (!buckets[productType]) {
        buckets[productType] = [];
      }

      // Add ancillary to the appropriate bucket
      buckets[productType].push({
        planDesignIndex: 0,
        ancillaryIndex,
        ancillary,
      });
    }
  );

  return buckets;
};

/**
 * Creates a tab ID from a product type
 */
export const createTabId = (productType: string): string => {
  return productType
    .toLowerCase()
    .replace(/\s+/g, '-')
    .replace(/[^a-z0-9-]/g, '');
};

/**
 * Creates a product configuration object
 */
export const createProductConfig = (
  productType: string,
  basePath: string = PLAN_DESIGNS_BASE_PATH
): ProductConfig => {
  const tabId = createTabId(productType);
  return {
    productType,
    tabId,
    subtitle: `${productType} Products`,
    basePath,
  };
};

/**
 * Generates product configurations for all product types in the buckets
 */
export const generateProductConfigs = (
  productBuckets: ProductBuckets,
  basePath: string = PLAN_DESIGNS_BASE_PATH
): ProductConfig[] => {
  return Object.keys(productBuckets).map((productType) =>
    createProductConfig(productType, basePath)
  );
};

/**
 * Standard field paths for product fields
 */
export const PRODUCT_FIELD_PATHS = {
  name: 'product_name',
  effectiveDate: 'effective_date',
  expirationDate: 'expiration_date',
  eligibilityExports: 'eligibility_export_ind',
  pharmacyClaimsExport: 'pharmacy_claim_export_ind',
  product_option: 'product_optios',
  notes: 'notes',
};

/**
 * Standard field labels for product fields
 */
export const PRODUCT_FIELD_LABELS = {
  name: 'Product Name',
  effectiveDate: 'Effective Date',
  expirationDate: 'Expiration Date',
  eligibilityExports: 'Eligibility Exports',
  pharmacyClaimsExport: 'Pharmacy Claims Export',
  notes: 'Product/Program Specific Notes',
};

/**
 * Standard field types for product fields
 */
export const PRODUCT_FIELD_TYPES = {
  name: 'input',
  effectiveDate: 'datepicker',
  expirationDate: 'datepicker',
  eligibilityExports: 'dropdownSelect',
  pharmacyClaimsExport: 'dropdownSelect',
  notes: 'input',
};

/**
 * Returns the full path for a product field at a specific index
 * @param planDesignIndex The index of the plan design
 * @param ancillaryIndex The index of the plan ancillary
 * @param field The field to get the path for
 * @returns The full path string
 */
export function getProductPath(
  planDesignIndex: number,
  ancillaryIndex: number,
  field: keyof typeof PRODUCT_FIELD_PATHS
): string {
  const rawPath = PRODUCT_FIELD_PATHS[field];
  const replacedPath =
    typeof rawPath === 'string'
      ? rawPath.replace('[i]', ancillaryIndex.toString())
      : rawPath;
  return `${PLAN_DESIGNS_BASE_PATH}.${planDesignIndex}.plan_design_ancillaries.${ancillaryIndex}.${replacedPath}`;
}

/**
 * Returns the base path for plan design ancillaries
 * @param planDesignIndex The index of the plan design
 * @param isNewItem Whether this is for a new item
 * @param ancillaryIndex Optional index for existing items
 * @returns The base path string
 */
export function getProductBasePath(
  planDesignIndex: number,
  isNewItem: boolean,
  ancillaryIndex = -1
): string {
  return getBasePath(
    isNewItem,
    `${PLAN_DESIGNS_BASE_PATH}.${planDesignIndex}.plan_design_ancillaries`,
    ancillaryIndex
  );
}

/**
 * Returns an object with all field paths for a specific index
 * @param planDesignIndex The index of the plan design
 * @param ancillaryIndex The index of the plan ancillary
 * @returns An object with all field paths
 */
export function getProductPaths(
  planDesignIndex: number,
  ancillaryIndex: number
): Record<keyof typeof PRODUCT_FIELD_PATHS, string> {
  return Object.entries(PRODUCT_FIELD_PATHS).reduce((paths, [key, value]) => {
    const replacedPath =
      typeof value === 'string'
        ? value.replace('[i]', ancillaryIndex.toString())
        : value;
    return {
      ...paths,
      [key]: `${PLAN_DESIGNS_BASE_PATH}.${planDesignIndex}.plan_design_ancillaries.${ancillaryIndex}.${replacedPath}`,
    };
  }, {} as Record<keyof typeof PRODUCT_FIELD_PATHS, string>);
}
