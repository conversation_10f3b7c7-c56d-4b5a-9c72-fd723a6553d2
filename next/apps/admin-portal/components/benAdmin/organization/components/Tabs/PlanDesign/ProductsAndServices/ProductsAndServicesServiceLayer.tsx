import {
  getPharmacyNetwork,
  usePlanDesignIndex,
} from 'apps/admin-portal/components/benAdmin';
import { PlanDesignAncillary } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { createDynamicGroups } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';
import { UseFormReturn } from 'react-hook-form';

import { usePicklistMaps } from '../../../../maps/picklistMaps';
import {
  PHARMACY_NETWORK_ITEM,
  PRODUCT_SET_UP_ITEM,
} from '../../../ChangeRequestIntake/Navigation/navigationConstants';
import { createProductFieldsGenerator } from './Config/genericTemplateBuilder';
import { organizePlanDesignAncillaries } from './Config/productConfig';
import { getProductSetup } from './FieldGroups/productSetUp';

export const useProductsAndServicesGroups = (
  formMethods: UseFormReturn<any>
) => {
  const { handleSubmit, watch } = formMethods;
  const { planDesignIndex } = usePlanDesignIndex();
  const maps = usePicklistMaps();
  const planDesigns = watch('plan.plan_designs') || [];
  const planDesignAncillaries: PlanDesignAncillary[] =
    planDesigns[planDesignIndex]?.plan_design_ancillaries || [];

  //   // Function to organize ancillaries into buckets by product type
  //   const organizePlanAncillaries = (
  //     ancillaries: PlanAncillary[] = []
  //   ): ProductBuckets => {
  //     return ancillaries.reduce(
  //       (buckets: ProductBuckets, ancillary, ancillaryIndex) => {
  //         const productType = ancillary.product_type || 'unknown';
  //         if (!buckets[productType]) {
  //           buckets[productType] = [];
  //         }
  //         buckets[productType].push({ ...ancillary, ancillaryIndex });
  //         return buckets;
  //       },
  //       {}
  //     );
  //   };

  // Get all plan design ancillaries and organize them into buckets
  const productBuckets = organizePlanDesignAncillaries(planDesignAncillaries);

  // Function to convert product type to tab ID
  const createTabId = (productType: string): string => {
    // Convert to lowercase, replace spaces with hyphens, remove special characters
    return productType
      .toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[^a-z0-9-]/g, '');
  };

  // Create field generators for each bucket
  const createFieldsForBucket = (productType: string, subtitle: string) => {
    const bucket = productBuckets[productType] || [];
    // Pass both ancillary and its actual index
    const ancillaryEntries = bucket
      .filter(
        (item) => item.ancillary && item.ancillary.product_type === productType
      )
      .map((item) => ({
        ancillary: item.ancillary,
        ancillaryIndex: item.ancillaryIndex,
      }));
    return createProductFieldsGenerator(
      productType,
      subtitle,
      planDesignIndex,
      handleSubmit,
      formMethods,
      maps
    )(ancillaryEntries);
  };

  // Generate dynamic groups for each product type
  const generateDynamicGroups = (
    productType: string,
    subtitle: string,
    tabId: string
  ) => {
    const groups = createFieldsForBucket(productType, subtitle);
    return createDynamicGroups(groups, `${tabId}Group`, (group) => ({
      ...group,
      subtitle: group.subtitle,
      tab: tabId,
    }));
  };

  // Create dynamic groups for each product type found in the data
  const dynamicGroups = Object.keys(productBuckets).reduce(
    (acc, productType) => {
      const tabId = createTabId(productType);
      const subtitle = `${productType} Products`; // Create subtitle based on the actual product type
      const groups = generateDynamicGroups(productType, subtitle, tabId);

      return {
        ...acc,
        ...groups,
      };
    },
    {}
  );

  return {
    productsAndServices: {
      productSetUpGroup: {
        subtitle: 'Core Products - Product Set Up',
        fields: getProductSetup(watch(), planDesignIndex),
        columns: 3,
        handleSubmit,
        formMethods,
        editable: true,
        tab: PRODUCT_SET_UP_ITEM,
        register: formMethods.register,
      },
      pharmacyNetworkGroup: {
        subtitle: 'Core Products - Pharmacy Network',
        fields: getPharmacyNetwork(watch(), planDesignIndex),
        columns: 3,
        handleSubmit,
        formMethods,
        editable: true,
        tab: PHARMACY_NETWORK_ITEM,
        register: formMethods.register,
      },
      ...dynamicGroups,
    },
  };
};
