{"Exclude": {"0": "Do Not Exclude", "1": "Exclude"}, "StudentAgeIndicator": {"1": "To Age", "2": "To End of Year", "3": "To End of Month", "4": "To End of Next Month"}, "DependentAgeIndicator": {"1": "To Age", "2": "To End of Year", "3": "To End of Month", "4": "To End of Next Month"}, "YesNo": {"0": "No", "1": "Yes"}, "YesNoNa": {"0": "No", "1": "Yes", "2": "Not Applicable"}, "YesNoEsi": {"0": "No", "1": "Yes", "2": "ESI PPT"}, "YesOtherAddNote": {"Yes": "Yes", "Other Add Note": "Other Add Note"}, "YesNoViewBenefit": {"0": "No", "1": "Yes", "View on Benefit": "View on Benefit"}, "Active": {"0": "Not Active", "1": "Active"}, "ActiveInactive": {"0": "Inactive", "1": "Active"}, "CoverExclude": {"0": "Exclude", "1": "Cover"}, "SuccessFail": {"0": "Failed", "1": "Successful"}, "PharmacyBilling": {"1": "Direct", "2": "None", "3": "No Bill/No Pay"}, "PharmacyPricing": {"1": "PBM Network", "2": "Submitted Pricing", "3": "Custom Rates"}, "PharmacyStatus": {"0": "Open", "1": "Finalized", "2": "Active", "9": "Terminated"}, "PharmacyOwnership": {"1": "Client Owned Pharmacy (IHP)", "2": "Client Contracted Pharmacy", "3": "Client Non-Owned Pharmacy (IHP)"}, "PharmacyChannel": {"1": "Mail", "2": "Retail", "3": "Specialty", "4": "In-House Pharmacies", "5": "Custom"}, "PaperClaimsPricing": {"0": "Not Covered", "1": "Submitted", "2": "Contracted"}, "PrePackagedCopay": {"1": "One Copay", "2": "Two Copay", "3": "Three Copay", "4": "2.5 Times", "5": "Mail Copay", "6": "MO Copay/Coins", "7": "Follows Retail", "8": "Follows Mail", "9": "Other"}, "PrepackagedCopays": {"1": "One Copay", "2": "Two Copay", "3": "Three Copay", "4": "2.5 Times", "5": "Mail Copay", "6": "MO Copay/Coins", "7": "Follows Retail", "8": "Follows Mail", "9": "Other"}, "DiabeticSuppliesCopay": {"1": "Bundled", "2": "Separate", "3": "<PERSON><PERSON><PERSON> (All)"}, "SpecialtyArrangement": {"0": "N/A - Specialty Meds Not Covered", "1": "Carve-Out", "2": "Exclusive Specialty", "3": "FL Open Specialty Pharmacy Network", "5": "No", "6": "Open Specialty", "7": "Open Specialty – My Pharmacy Options", "8": "Open Specialty – NO My Pharmacy Options (Specialty)", "9": "Precision Specialty", "10": "Precision Specialty Network", "11": "Preferred Specialty", "12": "Specialty Plus", "13": "Yes"}, "DispenseAsWritten": {"0": "None", "1": "DAW 2 Generic Copay + Difference", "2": "DAW 2 - Brand Copay + Difference", "3": "DAW 1 & 2 Generic Copay + Difference", "4": "DAW 1 & 2 Brand Copay + Difference", "7": "DAW 1 & 2 100% of Brand Cost", "8": "DAW 2 100% of Brand Cost", "11": "Other", "View on Benefit": "View on Benefit"}, "CompoundCopay": {"1": "Tiered", "2": "Brand", "3": "Most Expensive Ingredient", "4": "Preferred Brand Copay"}, "NetworkStatus": {"1": "In and Out of Network (Standard)", "2": "INN - In Network Only", "3": "OON - Out of Network Only", "999": "In-house/Preferred Pharmacy"}, "CostShareTier": {"$150 per 30 days supply": "$150 per 30 days supply", "Brand": "Brand", "Compound Medications": "Compound Medications", "Contraceptive Generic": "Contraceptive Generic", "Contraceptive Non-Preferred Brand": "Contraceptive Non-Preferred Brand", "Contraceptive Preferred Brand": "Contraceptive Preferred Brand", "Custom Drug List": "Custom Drug List", "Diabetic Generic": "Diabetic Generic", "Diabetic Medications": "Diabetic Medications", "Diabetic Non-Preferred Brand": "Diabetic Non-Preferred Brand", "Diabetic Preferred Brand": "Diabetic Preferred Brand", "Diabetic Supplies": "Diabetic Supplies", "Drug Specific": "Drug Specific", "EBPD Generic": "EBPD Generic", "EBPD Non-Preferred Brand": "EBPD Non-Preferred Brand", "EBPD Preferred Brand": "EBPD Preferred Brand", "Generic": "Generic", "Generic/ Preferred Brand / Non-Preferred Brand": "Generic/ Preferred Brand / Non-Preferred Brand", "Generic - Drug Based": "Generic - Drug Based", "Generic - Formulary Based": "Generic - Formulary Based", "IHP Multi Source Non-Preferred Brand": "IHP Multi Source Non-Preferred Brand", "IHP Multi Source Preferred Brand": "IHP Multi Source Preferred Brand", "IHP Single Source Non-Preferred Brand": "IHP Single Source Non-Preferred Brand", "IHP Single Source Preferred Brand": "IHP Single Source Preferred Brand", "In House Pharmacy Custom Drug List": "In House Pharmacy Custom Drug List", "In House Pharmacy Generic": "In House Pharmacy Generic", "In House Pharmacy Non-Preferred Brand": "In House Pharmacy Non-Preferred Brand", "In House Pharmacy Preferred Brand": "In House Pharmacy Preferred Brand", "Low Cost Generic List": "Low Cost Generic List", "Maintenance Brand SS and MS": "Maintenance Brand SS and MS", "Maintenance Generic": "Maintenance Generic", "Maintenance Non-Preferred Brand Medications": "Maintenance Non-Preferred Brand Medications", "Maintenance Preferred Brand Medications": "Maintenance Preferred Brand Medications", "Multi-Source Brand": "Multi-Source Brand", "Multi Source Non-Preferred Brand": "Multi Source Non-Preferred Brand", "Multi Source Preferred Brand": "Multi Source Preferred Brand", "Non-Preferred Brand": "Non-Preferred Brand", "OTC Program": "OTC Program", "Preferred Brand": "Preferred Brand", "Preferred Pharmacy Custom Drug List": "Preferred Pharmacy Custom Drug List", "Preferred Pharmacy Generic": "Preferred Pharmacy Generic", "Preferred Pharmacy Non-preferred Brand": "Preferred Pharmacy Non-preferred Brand", "Preferred Pharmacy Preferred Brand": "Preferred Pharmacy Preferred Brand", "Preferred pharmacy Specialty": "Preferred Pharmacy Specialty", "Preferred Pharmacy Specialty Generic": "Preferred Pharmacy Specialty Generic", "Preferred Pharmacy Specialty Non-Preferred Brand": "Preferred Pharmacy Specialty Non-Preferred Brand", "Preferred Pharmacy Specialty Preferred Brand": "Preferred Pharmacy Specialty Preferred Brand", "Preventive Generic": "Preventive Generic", "Preventive Medications": "Preventive Medications", "Preventive Non-Preferred Brand": "Preventive Non-Preferred Brand", "Preventive Preferred Brand": "Preventive Preferred Brand", "Self Injectable": "Self Injectable", "Single Source Brand": "Single Source Brand", "Single Source Non-Preferred Brand": "Single Source Non-Preferred Brand", "Single Source Preferred Brand": "Single Source Preferred Brand", "Single Tier Maint GN/PB/NPB": "Single Tier Maint GN/PB/NPB", "Specialty": "Specialty", "Specialty Brand": "Specialty Brand", "Specialty Generic": "Specialty Generic", "Specialty Multi-Source": "Specialty Multi-Source", "Specialty Non-Preferred Brand": "Specialty Non-Preferred Brand", "Specialty Preferred Brand": "Specialty Preferred Brand", "Specialty Single Source": "Specialty Single Source", "Tobacco Cessation Generic": "Tobacco Cessation Generic", "Tobacco Cessation Non-Preferred Brand": "Tobacco Cessation Non-Preferred Brand", "Tobacco Cessation Preferred Brand": "Tobacco Cessation Preferred Brand", "Value Generic": "Value Generic"}, "CostShareType": {"1": "Co-Pay", "2": "Co-Insurance", "3": "Co-Pay / Co-Insurance (Blended)"}, "CostShareTierDaysSupply": {"01-30": "01-30", "01-31": "01-31", "01-34": "01-34", "01-60": "01-60", "01-90": "01-90", "01-100": "01-100", "31-60": "31-60", "32-60": "32-60", "32-62": "32-62", "35-68": "35-68", "31-90": "31-90", "32-90": "32-90", "35-90": "35-90", "61-90": "61-90", "63-90": "63-90", "69-90": "69-90", "3X": "Multiplier (3x retail copay) = 3x", "2.5X": "Multiplier (2.5x retail copay)", "2X": "Multiplier (2x retail copay)", "DM30": "Days Multiplier (1 base retail copay per retail 30 days supply)", "DM31": "Days Multiplier (1 base retail copay per retail 31 days supply)", "DM34": "Days Multiplier (1 base retail copay per retail 34 days supply)", "31-100": "31-100", "32-100": "32-100", "35-100": "35-100", "61-100": "61-100"}, "CoInsuranceLesserGreater": {"0": "Lesser", "1": "Greater"}, "LineOfBusiness": {"Commercial": "Commercial", "Strategic": "Strategic"}, "Industry": {"Accounting": "Accounting", "Advertising": "Advertising", "Aerospace": "Aerospace", "Agency/Broker": "Agency/Broker", "Agriculture & Agribusiness": "Agriculture & Agribusiness", "Air Transportation": "Air Transportation", "Apparel & Accessories": "Apparel & Accessories", "Association": "Association", "Auto": "Auto", "Banking": "Banking", "Beauty & Cosmetics": "Beauty & Cosmetics", "Biotechnology": "Biotechnology", "Casino": "Casino", "Chemical": "Chemical", "Coalition / Association": "Coalition / Association", "Communications": "Communications", "Computer": "Computer", "Construction": "Construction", "Consulting": "Consulting", "Consumer Products": "Consumer Products", "Distributor": "Distributor", "Education": "Education", "Electronics": "Electronics", "Employer": "Employer", "Employment": "Employment", "Energy": "Energy", "Engineering": "Engineering", "Entertainment & Recreation": "Entertainment & Recreation", "Environmental": "Environmental", "Fashion": "Fashion", "Financial Services": "Financial Services", "Fine Arts": "Fine Arts", "Food & Beverage": "Food & Beverage", "Health": "Health", "Hospital": "Hospital", "Hospitality": "Hospitality", "Information Technology": "Information Technology", "Insurance": "Insurance", "Insurance Carrier": "Insurance Carrier", "Journalism & News": "Journalism & News", "Labor/Trust": "Labor/Trust", "Legal Services": "Legal Services", "Manufacturing": "Manufacturing", "Media & Broadcasting": "Media & Broadcasting", "Medical Devices & Supplies": "Medical Devices & Supplies", "Motion Pictures & Video": "Motion Pictures & Video", "Municipality": "Municipality", "Music": "Music", "Onsite Clinic Provider": "Onsite Clinic Provider", "Other": "Other", "Pharmaceutical": "Pharmaceutical", "Public Administration": "Public Administration", "Public Relations": "Public Relations", "Publishing": "Publishing", "Real Estate": "Real Estate", "Retail": "Retail", "Service": "Service", "TPA": "TPA", "Technology": "Technology", "Telecommunications": "Telecommunications", "Tourism": "Tourism", "Transportation": "Transportation", "Travel": "Travel", "Tribal Entity": "Tribal Entity", "Union": "Union", "Utilities": "Utilities", "Video Game": "Video Game", "Web Services": "Web Services"}, "IndustryVertical": {"Commercial": "Commercial", "Hospital/Health Systems": "Hospital/Health Systems", "Labor/Trust": "Labor/Trust", "Multi-Employer": "Multi-Employer", "None": "None", "Other": "Other", "Private Equity": "Private Equity", "TPA": "TPA", "Tribal Entity": "Tribal Entity"}, "TerminationReason": {"Acquisition": "Acquisition", "Alternative PBM": "Alternative PBM", "Anti-Steering Legislation": "Anti-Steering Legislation", "Bankruptcy": "Bankruptcy", "Broker Change/Broker Lost Client": "Broker Change/Broker Lost Client", "Carve-In Arrangement": "Carve-In Arrangement", "Child Account Left TPA": "Child Account Left TPA", "Coalition": "Coalition", "Fully Insured Arrangement": "Fully Insured Arrangement", "Implementation Issue (Prior)": "Implementation Issue (Prior)", "Key Decision Maker Change/Relationship Influence": "Key Decision Maker Change/Relationship Influence", "Medical Carrier Change/Limitations": "Medical Carrier Change/Limitations", "Merged with Existing Account": "Merged with Existing Account", "Pricing": "Pricing", "Relationship Degradation (Client)": "Relationship Degradation (Client)", "Relationship Degradation (Local Broker Office)": "Relationship Degradation (Local Broker Office)", "RxB Does Not Offer Desired Product": "RxB Does Not Offer Desired Product", "RxB Savings Gap": "RxB Savings Gap", "RxB Service Issues": "RxB Service Issues", "RxB Strategic Decision/Declined to Bid": "RxB Strategic Decision/Declined to Bid", "Specialty Management": "Specialty Management", "Stop Loss Concerns": "Stop Loss Concerns"}, "LegalEntityType": {"0": "Other", "1": "Organization", "2": "<PERSON><PERSON><PERSON>", "3": "Pharmacy", "4": "Medical Vendor"}, "State": {"AL": "Alabama", "AK": "Alaska", "AZ": "Arizona", "AR": "Arkansas", "CA": "California", "CO": "Colorado", "CT": "Connecticut", "DE": "Delaware", "FL": "Florida", "GA": "Georgia", "HI": "Hawaii", "ID": "Idaho", "IL": "Illinois", "IN": "Indiana", "IA": "Iowa", "KS": "Kansas", "KY": "Kentucky", "LA": "Louisiana", "ME": "Maine", "MD": "Maryland", "MA": "Massachusetts", "MI": "Michigan", "MN": "Minnesota", "MS": "Mississippi", "MO": "Missouri", "MT": "Montana", "NE": "Nebraska", "NV": "Nevada", "NH": "New Hampshire", "NJ": "New Jersey", "NM": "New Mexico", "NY": "New York", "NC": "North Carolina", "ND": "North Dakota", "OH": "Ohio", "OK": "Oklahoma", "OR": "Oregon", "PA": "Pennsylvania", "RI": "Rhode Island", "SC": "South Carolina", "SD": "South Dakota", "TN": "Tennessee", "TX": "Texas", "UT": "Utah", "VT": "Vermont", "VA": "Virginia", "WA": "Washington", "WV": "West Virginia", "WI": "Wisconsin", "WY": "Wyoming"}, "ErisaStatus": {"0": "Non-ERISA", "1": "ERISA"}, "IdCardsResponsible": {"1": "Medical Vendor", "2": "Other(Medical Vendor, TPA, etc.)", "3": "PBM", "4": "PBM/Medical Vendor", "5": "RxBenefits"}, "IdCardType": {"1": "Separate", "2": "Combined", "3": "Separate/Combined"}, "EmployeeIdSource": {"1": "Client ID", "2": "Client ID/Generated ID", "3": "Generated ID", "4": "Social Security Number"}, "IdCardMailing": {"1": "Member's Home", "2": "Group HR Office"}, "MemberPackets": {"1": "Bulk", "2": "Electronic", "3": "Electronic and Mail", "4": "Mail"}, "HealthcareReformStatus": {"1": "Grandfathered", "2": "Non-Grandfathered"}, "BenefitPeriods": {"1": "January through December (Calendar Year)", "2": "February through January", "3": "March through February", "4": "April through March", "5": "May through April", "6": "June through May", "7": "July through June", "8": "August through July", "9": "September through August", "10": "October through September", "11": "November through October", "12": "December through November"}, "NetworkApplicability": {"0": "Does Not Apply", "1": "INN to OON and OON to INN", "2": "OON To INN Only", "3": "INN And OON Are Separate (No Cross Accumulation)"}, "SpendingAccountType": {"0": "No Spending Account <PERSON>tached", "1": "HSA Setup", "2": "HRA Setup"}, "HraMembersAccess": {"1": "Debit Card", "2": "Real-Time POS", "3": "Direct Claim"}, "PlanDesignType": {"Copay Coinsurance": "Copay Coinsurance", "HDHP": "HDHP"}, "AccumTransfer": {}, "CobAllowed": {"3": "No", "4": "Yes - Electronic", "5": "Yes - Paper", "6": "Yes - Electronic and Paper"}, "MedicaidSubrogation": {"1": "Client", "2": "Medical Vendor", "3": "Other", "4": "PBM", "5": "TPA"}, "Integrated": {"1": "Integrated", "2": "Separate"}, "Embedded": {"1": "Embedded", "2": "Non-Embedded"}, "CarryoverPhase": {"0": "No", "1": "Yes", "2": "Fourth Quarter"}, "PenaltiesApply": {"0": "No", "1": "Yes", "2": "View on Benefit"}, "BenefitPeriodLength": {"1": "12 Months (Standard)", "2": "Lifetime", "3": "Other"}, "AccumDrugList": {"**********": "100080 - Impotency", "**********": "100088 - Fertility Agents (Std, All)", "**********": "100097 - <PERSON><PERSON><PERSON> (Std)", "**********": "100099 - Emergency Allergic Kits", "*********5": "100125 - Antihemophilia Agents", "*********7": "100127 - Smoking Deterrents", "*********8": "100128 - Smoking Cessation Medications", "*********9": "100129 - Smoking Deterrents", "**********": "100132 - Weight Loss (Anti-Obesity) - All", "**********": "100133 - Anti-Obesity Standard", "**********": "100257 - Diabetic Supplies/Insulin Needles, Syringes/Insulin (Std)", "**********": "100276 - Minimed insulin pumps, batteries, Sil-Serter, reservoir, OH-Tron Plus Pumps, OD-Tron pumps", "0000100282": "100282 - <PERSON><PERSON> and Syringes", "0000100355": "100355 - Iron Supplements", "0000100362": "100362 - Self Administered Injectibles", "0000100400": "100400 - <PERSON><PERSON><PERSON> Assisting Devices", "0000100404": "100404 - Blood Glucose Monitors w/glucowatch", "0000100429": "100429 - Glucowatch products", "0000100466": "100466 - <PERSON><PERSON><PERSON> and Syringes (Std)", "0000100497": "100497 - Injectable Specialty", "0000100498": "100498 - Peak Flow Meters", "0000100499": "100499 - <PERSON><PERSON><PERSON> Glo<PERSON>ins", "0000100508": "100508 - Prilosec OTC", "0000100617": "100617 - Bronchodilators", "0000100619": "100619 - Xant<PERSON>s STD", "0000100632": "100632 - <PERSON><PERSON> Claritin/Claritin-D", "0000100668": "100668 - Hypoglycemics Rx and OTC", "0000100685": "100685 - Custom Drug List TracPac", "0000100698": "100698 - Chronic Conditions", "0000100956": "100956 - Non-Injectable Specialty", "0000101445": "101445 - HIV Medications", "0000101847": "101847 - Fluticasone/ Salmeterol/Advair", "0000102304": "102304 - Injectables, All except Insulin and Emergency Allergic Rxn", "0000102695": "102695 - Antidepressants", "0000103905": "103905 - Nebulizer Equipment", "0000104281": "104281 - Std Rx/OTC equivalents", "0000105810": "105810 - Blood Sugar Test Strips", "0000105980": "105980 - Sub Q Insulin Pumps (STD)", "0000111683": "111683 - Non-sedating Antihistamines, OTC, Generic (std)", "0000111684": "111684 - Proton Pump Inhibitors, OTC, Generic (std)", "0000112276": "112276 - Zimmer Drug List", "0000113626": "113626 - Cholesterol", "0000113761": "113761 - <PERSON><PERSON><PERSON>", "0000117342": "117342 - Only <PERSON><PERSON> Supplies, No Pumps", "0000127369": "127369 - Legend Generic PPI's (does not include OTC's)", "0000129940": "129940 - Antihistamines, OTC Generic only", "0000130154": "130154 - FERTILITY AGENTS (ALL) LUPRON 1 AND PROGESTERONE OIL", "0000132282": "132282 - Fertility Regulators", "0000135007": "135007 - GROWTH PROMOTING AGENTS", "0000135008": "135008 - ED Meds", "0000136100": "136100 - Diabetic Supplies", "0000136380": "136380 - EBD:ESTROGEN REPLACE-90D", "0000136847": "136847 - Hyper and Hypoglycemics", "0000143981": "143981 - <PERSON>J<>INSULIN (DOSE14/50): (DF 14/50)", "0000150907": "150907 - <PERSON><PERSON><PERSON>ss, OTC", "0000153162": "153162 - Azathioprine brand and generics", "0000153685": "153685 - Static EBD00077: EBD: VBID ASTHMA-COPD DRUGS", "0000161282": "161282 - ACA Merged Offering Vitamin D and Calcium/Vitamin D", "0000162641": "162641 - Nitrates", "0000188601": "188601 - Bowel Prep Agents - Generics and SSB", "0000217827": "217827 - Std All Inclusive", "0000225585": "225585 - Diabetic Medications", "0000231291": "231291 - Standard Preventive Meds", "0000272929": "272929 - Standard GN Preventive Meds", "0000303862": "303862 - Standard plus Preventive Meds", "0000315711": "315711 - Specialty DL", "0000318233": "318233 - Unysis Custom Drug List", "0000337466": "337466 - Static Standard", "0000339109": "339109 - Standard plus GN Preventive Meds", "0000339878": "339878 - Nexium OTC", "0000359071": "359071 - Springpoint Senior Living, Inc.", "0000380046": "380046 - Female HDSS (Female Sexual Disfunction)", "0000410332": "410332 - Diabetic Meds and Supplies (Generic and Brand)", "0000411136": "411136 - Cardiovascular/Cardiac Drugs", "0000411198": "411198 - <PERSON><PERSON><PERSON><PERSON><PERSON>b", "0000432501": "432501 - Hypertensives/Blood Pressure", "0000485657": "485657 - Northeastern Supply, Inc Custom DL", "0000492390": "492390 - Custom DL (Specialty & Injectables & Non Injectable)", "0000492409": "492409 - Custom DL (Specialty & Injectables & Non Injectable)", "0000497641": "497641 - SaveOnSP DL", "0000498866": "498866 - RxB LCG", "0000506697": "506697 - <PERSON><PERSON><PERSON> (STD)", "0000524304": "524304 - master Vaccine Program drug list (142422) minus 171663", "0000542011": "542011 - Diabetic Supplies - Omnipod (ONLY)", "0000542295": "542295 - Custom Preventative Drug List", "0000544426": "544426 - Landscape Structures Custom Asthma DL", "0000544858": "544858 - <PERSON><PERSON>", "0000565223": "565223 - Tx Baptist Custom", "0000566078": "566078 - Urinary Tract Antispasmodic", "0000592085": "592085 - Weight Management Care Value meds", "0000594835": "594835 - PAP Drug List for NPF", "0000596496": "596496 - PAP list Basic", "0000597428": "597428 - DME (OmniPod Dash)", "0000603673": "603673 - <PERSON><PERSON> Hyaluronate Copay DL", "0000607463": "607463 - Std Plus Prev Med DL + PAP for Basic Form", "0000607474": "607474 - Std Plus Prev Med DL Minus PAP for NPF", "0000607957": "607957 - Generic Antidiabetic Meds", "0000613473": "613473 - NonACA PM/Oral Anticancer agents", "0000614273": "614273 (STD HDHP G, gen PPI, All Ins)", "0000616110": "616110 - Covid Oral Antiviral", "0000626600": "626600 - GOHIO DM Med List", "0000631687": "631687 - Payer Matrix Drug List", "0000641225": "641225 - DL RSV STD DL", "0000641684": "641684 - <PERSON> - Child of DL 595435", "0000660690": "660690 - RxB Standard Plus CDH minus GTC 08", "0000000000": "No Include Drug List"}, "DocumentInternal": {"1": "Internal", "2": "External", "3": "Both"}, "ImplementationTimeline": {"0": "Quick Start", "45": "45 Day", "60": "60 Day", "90": "90 Day"}, "ExpressionContext": {"1": "Plan Validation", "2": "Show/Hide Fields", "3": "Default Values"}, "ExpressionEntityScope": {"plan": "Plan", "plan_design_detail": "Plan Design Detail"}, "PlanValidationRuleScope": {"1": "Product", "2": "Product Class"}, "PlanValidationRuleSeverity": {"1": "Error", "2": "Warning"}, "Months": {"1": "January", "2": "February", "3": "March", "4": "April", "5": "May", "6": "June", "7": "July", "8": "August", "9": "September", "10": "October", "11": "November", "12": "December"}, "OtherAccumType": {"5": "Benefit Cap", "7": "Lifetime Cap", "9": "Drug List Specific Cap"}, "PlanClass": {"1": "Self Funded", "2": "<PERSON>"}, "PriorAuthorizationReviewer": {"PBM": "PBM", "RxBenefits": "RxBenefits"}, "ESI-RetailPharmacyNetwork": {"National Plus Network": "National Plus Network", "Advantage Network": "Advantage Network", "National Network": "National Network", "Express Advantage Network - Incentive": "Express Advantage Network - Incentive", "N/A": "N/A"}, "ESI-MaintenancePharmacyNetwork": {"Smart 90 Key Anchors Voluntary": "Smart 90 Key Anchors Voluntary", "Smart 90 Key Anchors Exclusive": "Smart 90 Key Anchors Exclusive", "Smart 90 Key Anchors Incentive": "Smart 90 Key Anchors Incentive", "Smart90 Standard Voluntary": "Smart90 Standard Voluntary", "Smart90 Standard Exclusive": "Smart90 Standard Exclusive", "Smart90 Standard Incentive": "Smart90 Standard Incentive", "Smart90 CVS Voluntary": "Smart90 CVS Voluntary", "Smart90 CVS Exclusive": "Smart90 CVS Exclusive", "Smart90 CVS Incentive": "Smart90 CVS Incentive", "Smart90 Walgreens Voluntary": "Smart90 Walgreens Voluntary", "Smart90 Walgreens Exclusive": "Smart90 Walgreens Exclusive", "Smart90 Walgreens Incentive": "Smart90 Walgreens Incentive", "Smart90 Anywhere Voluntary": "Smart90 Anywhere Voluntary", "Smart90 Anywhere Exclusive": "Smart90 Anywhere Exclusive", "Smart90 Anywhere Incentive": "Smart90 Anywhere Incentive", "Standard Maintenance Network": "Standard Maintenance Network", "N/A": "N/A"}, "ESI-SpecialtyPharmacyNetwork": {"Open Specialty Network": "Open Specialty Network", "Precision Specialty Network": "Precision Specialty Network", "Exclusive Specialty": "Exclusive Specialty", "Carve-out Specialty": "Carve-out Specialty"}, "ESI-SpecialtyGraceFillsRetail": {"1 nonSTAT; STAT unlimited": "1 nonSTAT; STAT unlimited", "2 nonSTAT;STAT unlimited": "2 nonSTAT;STAT unlimited", "3 nonSTAT; STAT unlimited": "3 nonSTAT; STAT unlimited", "zero nonSTAT; STAT unlimited": "zero nonSTAT; STAT unlimited", "1 nonSTAT; 1 STAT": "1 nonSTAT; 1 STAT", "2 nonSTAT; 2 STAT": "2 nonSTAT; 2 STAT", "3 nonSTAT; 3 STAT": "3 nonSTAT; 3 STAT", "0 nonSTAT; 0 STAT": "0 nonSTAT; 0 STAT"}, "ESI-HomeDeliveryProgram": {"Exclusive Home Delivery": "Exclusive Home Delivery", "Select - Incentive Choice": "Select - Incentive Choice", "Voluntary Smart89": "Voluntary Smart89", "Exclusive Smart90": "Exclusive Smart90", "Active Choice Smart90": "Active Choice Smart90", "Incentive Choice Smart990": "Incentive Choice Smart990", "My Pharmacy Options": "My Pharmacy Options", "Select - Active Choice": "Select - Active Choice", "Open - IHP Only": "Open - IHP Only"}, "ESI-FormularyName": {"Basic Formulary": "Basic Formulary", "National Preferred Formulary": "National Preferred Formulary"}, "ESI-IrsHdhpPreventativeList": {"None": "None", "272929 Standard Generic Only": "272929 Standard Generic Only", "231291 Standard Generic and Brand": "231291 Standard Generic and Brand", "339109 Standard Plus Generic Only": "339109 Standard Plus Generic Only", "303862 Standard Plus Generic and Brand": "303862 Standard Plus Generic and Brand"}, "ESI-UtilizationManagementBundle": {"Level 1": "Level 1", "Super Advantage": "Super Advantage", "Super Advantage Plus": "Super Advantage Plus", "Limited": "Limited", "Advantage Plus": "Advantage Plus", "Advantage": "Advantage", "Unlimited": "Unlimited"}, "ESI-DrugList": {"0": "N/A No Drug List ID Needed", "100042": "100042 - Contraceptive Medications and Devices (Std, All OTC/Legend)", "100080": "100080 - Impotency", "100088": "100088 - Fertility", "100097": "100097 - <PERSON><PERSON><PERSON> (Std)", "100098": "100098 - Glucagon", "100099": "100099 - Emergency Allergic Kits", "100125": "100125 - Antihemophilia Agents", "100127": "100127 - Smoking Deterrents", "100128": "100128 - Smoking Cessation Medications", "100132": "100132 - Weight Loss (Anti-Obesity) - All", "100133": "100133 - Anti-Obesity Federal Legend", "100142": "100142 - Proton Pump Inhibitors (Std, All)", "100257": "100257 - Diabetic Supplies/Insulin Needles, Syringes/Insulin (Std)", "100258": "100258 - Diabetic Supplies/Insulin <PERSON>, Syringes (Std)", "100260": "100260 - Hyper- and Hypoglycemics (Std, OTC and Legend)", "100276": "100276 - Minimed insulin pumps, batteries, Sil-Serter, reservoir, OH-Tron Plus Pumps, OD-Tron pumps", "100355": "100355 - Iron Supplements", "100362": "100362 - Self-Administered Injectables (Std)", "100400": "100400 - <PERSON><PERSON><PERSON> Assisting Devices (Std)", "100403": "100403 - <PERSON><PERSON>", "100404": "100404 - Blood Glucose Monitors w/glucowatch", "100429": "100429 - Glucowatch products", "100466": "100466 - <PERSON><PERSON><PERSON> and Syringes (Std)", "100497": "100497 - Injectable Specialty", "100498": "100498 - Peak Flow Meters", "100499": "100499 - <PERSON><PERSON><PERSON> Glo<PERSON>ins", "100508": "100508 - Prilosec OTC", "100601": "100601 - Antineoplastics (Std, All) anticancer agents", "100604": "100604 - Non-Insulin <PERSON>les and Syringes (Std)", "100608": "100608 - OTCs (Std, All)", "100617": "100617 - Bronchodilators", "100619": "100619 - Xant<PERSON>s STD", "100620": "100620 - Inhaled Corticosteroids (Std, All)", "100632": "100632 - <PERSON><PERSON> Claritin/Claritin-D", "100637": "100637 - <PERSON><PERSON> (Std)", "100640": "100640 - BGM‚Äôs except Glucowatch", "100668": "100668 - Hypoglycemics Rx and OTC", "100685": "100685 - Custom Drug List TracPac", "100698": "100698 - Chronic Conditions", "100784": "100784 - Non-Sedating Antihistamines (Std, OTC)", "100787": "100787 - Proton Pump Inhibitors (Std, OTC)", "100956": "100956 - Non-Injectable Specialty", "101445": "101445 - HIV Medications", "101768": "101768 - H2 Antagonists (OTC)", "101847": "101847 - Fluticasone/ Salmeterol/Advair", "101876": "101876 - Antineoplasitcs, oral (All) anticancer agents", "102258": "102258 - <PERSON><PERSON><PERSON>/Cartridges", "102304": "102304 - Injectables, All except Insulin and Emergency Allergic Rxn", "102695": "102695 - Antidepressants", "103309": "103309 - CII - Controlled Substance", "103905": "103905 - Nebulizer Equipment", "104281": "104281 - Std Rx/OTC equivalents", "105810": "105810 - Blood Sugar Test Strips", "105980": "105980 - Sub Q Insulin Pumps (STD)", "111683": "111683 - Non-sedating Antihistamines, OTC, Generic (std)", "111684": "111684 - Proton Pump Inhibitors, OTC, Generic (std)", "112276": "112276 - Zimmer Drug List", "113626": "113626 - Cholesterol", "113761": "113761 - <PERSON><PERSON><PERSON>", "114795": "114795 - ALL Diabetic Supplies, ALL Meds - OTC and Legend", "116021": "116021 - Standard Prepacks 91 Days Supply", "117201": "117201 - Antineoplastics, Oral, Specialty (All)", "117342": "117342 - Only <PERSON><PERSON> Supplies, No Pumps", "117647": "117647 - <PERSON><PERSON><PERSON> Brand Only", "127367": "127367 - Standard Prepacks", "127369": "127369 - Legend Generic PPI‚Äôs (does not include OTC‚Äôs)", "129940": "129940 - Antihistamines, OTC Generic only", "130154": "130154 - Fertility", "136100": "136100 - Diabetic Supplies", "136380": "136380 - EBD:ESTROGEN REPLACE-90D", "136847": "136847 - Hyper and Hypoglycemics", "143981": "143981 - <PERSON>J<>INSULIN (DOSE14/50): (DF 14/50)", "150907": "150907 - <PERSON><PERSON><PERSON>ss, OTC", "153162": "153162 - Azathioprine brand and generics", "153685": "153685 - Static EBD00077: EBD: VBID ASTHMA-COPD DRUGS", "161282": "161282 - ACA Merged Offering Vitamin D and Calcium/Vitamin D", "162641": "162641 - Nitrates", "188601": "188601 - Bowel Prep Agents - Generics and SSB", "217827": "217827 - Std All Inclusive", "225585": "225585 - Diabetic Medications", "225880": "225880 - Diabetic Supplies Limited", "231291": "231291 - Standard Preventive Medication List", "265460": "265460 - Lansopra<PERSON><PERSON>", "272929": "272929 - Standard Generic Preventive Medication List", "303862": "303862 - Standard Plus Preventive Medication List", "315711": "315711 - <PERSON><PERSON>", "318233": "318233 - Unysis Custom Drug List", "337466": "337466 - Static Standard", "339109": "339109 - Standard Plus Generic Preventive Medication List", "339878": "339878 - Nexium OTC", "359071": "359071 - Springpoint Senior Living, Inc.", "380046": "380046 - Female HDSS (Female Sexual Disfunction)", "410332": "410332 - Diabetic Meds and Supplies (Generic and Brand)", "411136": "411136 - Cardiovascular/Cardiac Drugs", "411198": "411198 - <PERSON><PERSON><PERSON><PERSON><PERSON>b", "426125": "426125 - EPIP<PERSON> AND EPIPEN FR: STANDARD", "432501": "432501 - Hypertensives/Blood Pressure", "485657": "485657 - Northeastern Supply, Inc Custom DL", "492390": "492390 - Custom DL (Specialty & Injectables & Non Injectable)", "492409": "492409 - Custom DL (Specialty & Injectables & Non Injectable)", "497641": "497641 - SaveOnSP DL", "498866": "498866 - RxB LCG", "506697": "506697 - <PERSON><PERSON><PERSON> (STD)", "524304": "524304 - master Vaccine Program drug list (142422) minus 171663", "525499": "525499 - PAP Insulin Only drug list", "527848": "527848 - RxB Low Cost Generics", "542011": "542011 - Diabetic Supplies - Omnipod (ONLY)", "542295": "542295 - Custom Preventative Drug List", "544426": "544426 - Landscape Structures Custom Asthma DL", "544858": "544858 - <PERSON><PERSON>", "565223": "565223 - Tx Baptist Custom", "566078": "566078 - Urinary Tract Antispasmodic", "592085": "592085 - Weight Management Care Value meds", "594835": "594835 - PAP Drug List for NPF", "596496": "596496 - PAP list Basic", "597428": "597428 - DME (OmniPod Dash)", "603673": "603673 - <PERSON><PERSON> Hyaluronate Copay DL", "607957": "607957 - Generic Antidiabetic Meds", "613473": "613473 - NonACA PM/Oral Anticancer agents", "614273": "614273 - (STD HDHP G, gen PPI, All Ins)", "616110": "616110 - Covid Oral Antiviral", "626600": "626600 - GOHIO DM Med List", "631687": "631687 - Payer Matrix Drug List", "641225": "641225 - DL RSV STD DL", "641684": "641684 - <PERSON> - Child of DL 595435"}, "EsiCopayTier": {"1": "Base Copay", "12": "Copay after fill limit is exceeded (RRA)", "19": "Drug Specific Copay (Exception) - requires Drug List", "76": "Drug Specific Copay during DED Phase - requires Drug List", "110": "Copays that apply to compounds", "Copays that Apply to Compounds": "Copays that Apply to Compounds"}, "EsiCopayChannel": {"1": "Mail Only", "2": "Mail & Direct", "3": "Card (Retail) Only", "4": "Direct (Paper) Only", "5": "Card, Direct & Mail", "6": "Card & Mail Only", "7": "Retail (Card & Direct) Only"}, "EsiCopayStructure": {"1": "Single Tier", "2": "Drug Source Based(Generic)", "3": "Drug Source Based (Single Source)", "4": "Drug Source Based(Multi Source)", "5": "Formulary Based (Generic)", "6": "Formulary Based (Pref)", "7": "Formulary Based (Non-Pref)", "8": "Drug Source Based (Brand - SS and MS)", "9": "DAW 2 Other (copay no penalty)", "10": "DAW 1 and 2 Other (copay no penalty)"}, "EsiCopayNetwork": {"M": "Maintenance Retail 90 Benefit (Phase II)", "I": "Specified <PERSON>y Applies for In Network", "O": "Specified <PERSON><PERSON> Applies for Out of Network Only", "B": "Specified Copay Applies to In Network & Out of Network"}, "EsiNinetyDayProgram": {"Active Choice SM90 Anywhere": "Active Choice SM90 Anywhere", "Active Choice SM90 CVS": "Active Choice SM90 CVS", "Active Choice SM90 Key Anchors": "Active Choice SM90 Key Anchors", "Active Choice SM90 WAG": "Active Choice SM90 WAG", "Advanced Choice": "Advanced Choice", "Broad/Open": "Broad/Open", "CVS": "CVS", "CVS 90 Saver": "CVS 90 Saver", "CVS 90 Saver Plus": "CVS 90 Saver Plus", "CVS Standard Select": "CVS Standard Select", "CVS Value Network": "CVS Value Network", "Custom": "Custom", "Exclusive SM90 Anywhere": "Exclusive SM90 Anywhere", "Exclusive SM90 CVS": "Exclusive SM90 CVS", "Exclusive SM90 Key Anchors": "Exclusive SM90 Key Anchors", "Exclusive SM90 WAG": "Exclusive SM90 WAG", "Extended Day Supply (Limited": "Extended Day Supply (Limited", "In House": "In House", "Incentive Choice SM90 CVS": "Incentive Choice SM90 CVS", "Incentive Choice SM90 Key Anchors": "Incentive Choice SM90 Key Anchors", "Incentive Choice SM90 WAG": "Incentive Choice SM90 WAG", "Incentive SM90 Anywhere": "Incentive SM90 Anywhere", "Limited - Ext Day": "Limited - Ext Day", "Limited Network": "Limited Network", "Limited Retail 90": "Limited Retail 90", "Maintenance Choice All Access": "Maintenance Choice All Access", "Mandatory Maintenance Choice": "Mandatory Maintenance Choice", "Narrow": "<PERSON>rrow", "National - Open": "National - Open", "No": "No", "None": "None", "Not Applicable": "Not Applicable", "Open": "Open", "Open Retail 90": "Open Retail 90", "Standard Maintenance R90": "Standard Maintenance R90", "Value": "Value", "Voluntary Maintenance Choice": "Voluntary Maintenance Choice", "Voluntary SM WAG": "Voluntary SM WAG", "Voluntary SM90 Anywhere": "Voluntary SM90 Anywhere", "Voluntary SM90 CVS": "Voluntary SM90 CVS", "Voluntary SM90 Key Anchors": "Voluntary SM90 Key Anchors", "Voluntary SM90 Standard": "Voluntary SM90 Standard", "WAG 90 Saver": "WAG 90 Saver", "WAG 90 Saver Plus": "WAG 90 Saver Plus", "WAG Standard Select": "WAG Standard Select", "WAG Value Network": "WAG Value Network", "Walgreens": "Walgreens", "Yes": "Yes"}, "EsiMandatorySpecialtyDaysSupply": {"0": "N/A", "90": "90", "1-30": "1-30", "1-31": "1-31", "1-34": "1-34"}, "EsiMandatorySpecialtyFillLimit": {"0": "zero nonSTAT; STAT unlimited", "1": "1 nonSTAT; STAT unlimited", "2": "2 nonSTAT; STAT unlimited", "3": "3 nonSTAT; STAT unlimited", "4": "1 nonSTAT; 1 STAT", "5": "2 nonSTAT; 2 STAT", "6": "3 nonSTAT; 3 STAT", "7": "0 nonSTAT; 0 STAT", "View on Benefit": "View on Benefit"}, "EsiRetailDaysSupply": {"1-30": "1-30", "1-31": "1-31", "1-34": "1-34", "1-40": "1-40", "1-60": "1-60", "1-90": "1-90", "N/A": "N/A"}, "EsiHomeDeliveryFillLimit": {"0": "0", "1": "1", "2": "2", "3": "3", "N/A": "N/A", "View on Benefit": "View on Benefit"}, "EsiHomeDeliveryProgram": {"0": "None", "1": "Exclusive Home Delivery", "2": "Select - Active Choice", "3": "Select - Incentive Choice", "4": "Voluntary Smart90", "5": "Incentive Choice Smart90", "6": "Active Choice Smart90", "7": "Exclusive Smart90", "8": "My Pharmacy Options", "9": "Open - IHP Only", "10": "My Pharmacy Options – Anti-Steerage Letter", "Maintenance Choice All Access": "Maintenance Choice All Access", "Mandatory Maintenance Choice": "Mandatory Maintenance Choice", "Open - No My Pharmacy Options": "Open - No My Pharmacy Options", "Select": "Select", "View on Benefit": "View on Benefit", "Voluntary Maintenance Choice": "Voluntary Maintenance Choice"}, "EsiPaperClaimsCovered": {"0": "Not Covered", "1": "Submitted", "2": "Contracted"}, "EsiCompoundManagementProgram": {"1": "Do Not Enroll - No coverage for compounds", "5": "Do Not Enroll - Compounds Covered via Primary NDC", "6": "Enroll", "Do Not Enroll": "Do Not Enroll", "No": "No"}, "EsiForeignClaims": {"0": "Yes", "2": "No", "3": "Emergency Only"}, "EsiAccumPeriod": {"C": "Calendar Year", "P": "Plan Year"}, "EsiDependentAge": {"1": "To Age", "2": "To End of Year", "3": "To End of Month", "4": "To End of Next Month"}, "EsiInsulinMethod": {"5": "Separate (Standard)", "8": "<PERSON><PERSON><PERSON> (Insulin First)", "G": "Bundled copays (All)"}, "EsiRetailNetwork": {"21735": "Ex Smart90 CVS with Precision Specialty", "21736": "Ex Smart90 WAGS with Precision Specialty", "21737": "Ex Smart90 Key Anchors with Precision Specialty", "21740": "Vol Smart90 Key Anchors with Precision Specialty", "21746": "Vol Smart90 CVS with Precision Specialty", "21747": "Vol Smart90 WAGS with Precision Specialty", "21748": "Standard Network with Precision Specialty", "21760": "Vol Smart90 Standard with Precision Specialty", "21892": "Exclusive SM90 Anywhere", "22240": "Incentive Choice SM90 CVS with base network National-CVS", "22330": "National Walgreens Network with Exclusive Smart90 Walgreens", "22336": "Active Choice SM90 WAG", "22502": "Voluntary SM90 Anywhere", "31127": "National Plus 2.0 - RxBenefits", "31408": "Exclusive Smart90 CVS", "31647": "Exclusive Smart90 Walgreens", "33333": "Custom - If none of the standard network selections apply, will require manual configuration", "35141": "Voluntary SMT90 Walgreens", "39569": "Voluntary SMT90 Standard", "42406": "Key Anchors Active/Incentive Choice SMT90", "47881": "Voluntary SMT90 CVS", "51387": "Key Anchors Voluntary SMT90", "52243": "Key Anchors Exclusive SMT90", "Direct Custom ESI Retail Network": "Direct Custom ESI Retail Network"}, "EsiClaimSubmissionType": {"1": "Mail Claims Only", "2": "Mail & Direct (Paper) Claims Only", "3": "Card Only Claims", "4": "Direct (Paper) Claims Only", "5": "Standard Paid - Card, Direct (Paper) & Mail Claims", "6": "Card & Mail Claims Only", "7": "Card and Direct (Paper) Claims Only"}, "OPT-RetailPharmacyNetwork": {"Standard Select Network": "Standard Select Network", "Broad Network": "Broad Network", "Value Network": "Value Network"}, "OPT-MaintenancePharmacyNetwork": {"CVS90 Saver Voluntary": "CVS90 Saver Voluntary", "CVS90 Saver Mandatory": "CVS90 Saver Mandatory", "CVS90 Saver Plus Voluntary": "CVS90 Saver Plus Voluntary", "CVS90 Saver Plus Mandatory": "CVS90 Saver Plus Mandatory", "Walgreen90 Saver Voluntary": "Walgreen90 Saver Voluntary", "Walgreen90 Saver Mandatory": "Walgreen90 Saver Mandatory", "Walgreen90 Saver Plus Voluntary": "Walgreen90 Saver Plus Voluntary", "Walgreen90 Saver Plus Mandatory": "Walgreen90 Saver Plus Mandatory", "Standard Retail Network": "Standard Retail Network", "CVS90 Value Voluntary": "CVS90 Value Voluntary", "CVS90 Value Mandatory": "CVS90 Value Mandatory", "Walgreen90 Value Voluntary": "Walgreen90 Value Voluntary", "Walgreen90 Value Mandatory": "Walgreen90 Value Mandatory", "CVS90 Standard Select 90": "CVS90 Standard Select 90", "Walgreen 90 Standard Select 90": "Walgreen 90 Standard Select 90", "N/A": "N/A"}, "OPT-SpecialtyPharmacyNetwork": {"Open Specialty Network": "Open Specialty Network", "Exclusive Specialty Network": "Exclusive Specialty Network", "Preferred Specialty Network": "Preferred Specialty Network", "Carve-out Specialty": "Carve-out Specialty"}, "OPT-MandatoryMailProgram": {"Mail Service Saver": "Mail Service Saver", "Mail Service Saver Plus": "Mail Service Saver Plus", "Optum Mail (Member Select)": "Optum Mail (Member Select)", "Optum Home Delivery - Standard": "Optum Home Delivery - Standard"}, "OPT-HomeDeliveryProgram": {"Mail Service Member Select": "Mail Service Member Select", "Mail Service Saver": "Mail Service Saver", "Mail Service Saver Plus": "Mail Service Saver Plus", "Optum Home Delivery - Standard": "Optum Home Delivery - Standard", "N/A": "N/A"}, "OPT-FormularyName": {"Select Formulary": "Select Formulary", "Premium Formulary": "Premium Formulary"}, "OPT-Abortifacients": {"Exclude": "Exclude", "Cover": "Cover", "Exclude - Covered by Medical": "Exclude - Covered by Medical", "Exclude - NOT Covered by Medical": "Exclude - NOT Covered by Medical", "Exclude - Covered by 3rd party vendor": "Exclude - Covered by 3rd party vendor", "Exclude - Religious/Moral exemption": "Exclude - Religious/Moral exemption", "Exclude - Prohibited from coverage due to state/federal law": "Exclude - Prohibited from coverage due to state/federal law"}, "OPT-AcaContraceptives": {"Cover": "Cover", "Exclude": "Exclude", "Exclude - Grandfathered Plan": "Exclude - Grandfathered Plan", "Exclude - Covered by Medical": "Exclude - Covered by Medical", "Exclude - NOT Covered by Medical": "Exclude - NOT Covered by Medical", "Exclude - Covered by 3rd party vendor": "Exclude - Covered by 3rd party vendor", "Exclude - Religious/Moral exemption": "Exclude - Religious/Moral exemption", "Exclude - Prohibited from coverage due to state/federal law": "Exclude - Prohibited from coverage due to state/federal law"}, "OPT-IrsHdhpPreventativeList": {"None": "None", "Premium Brand + Generics": "Premium Brand + Generics", "Premium Generics Only": "Premium Generics Only", "Select Brand + Generics": "Select Brand + Generics", "Select Generics Only": "Select Generics Only"}, "OPT-UtilizationManagementBundle": {"Focused": "Focused", "Comprehensive": "Comprehensive", "Comprehensive No Step Therapy": "Comprehensive No Step Therapy"}, "CMK-RetailPharmacyNetwork": {"Advanced Choice Network": "Advanced Choice Network", "Enhanced National Network": "Enhanced National Network", "National Network": "National Network"}, "CMK-MaintenancePharmacyNetwork": {"Extended Days Supply": "Extended Days Supply", "Home Delivery Service": "Home Delivery Service", "Maintenance Choice Mandatory": "Maintenance Choice Mandatory", "Maintenance Choice Opt Out": "Maintenance Choice Opt Out", "Maintenance Choice Voluntary": "Maintenance Choice Voluntary", "N/A": "N/A"}, "CMK-SpecialtyPharmacyNetwork": {"Exclusive Specialty Network": "Exclusive Specialty Network", "Expanded Specialty Network": "Expanded Specialty Network", "Open Specialty Network": "Open Specialty Network", "Carve-out Specialty": "Carve-out Specialty"}, "CMK-FormularyName": {"Advanced Control Formulary": "Advanced Control Formulary", "Basic Control Formulary": "Basic Control Formulary", "Basic Control Formulary + Advanced Control Specialty Formulary": "Basic Control Formulary + Advanced Control Specialty Formulary", "Standard Control Formulary": "Standard Control Formulary", "Standard Control Formulary + Advanced Control Specialty Formulary": "Standard Control Formulary + Advanced Control Specialty Formulary", "Standard Opt Out Formulary": "Standard Opt Out Formulary", "Standard Opt Out Formulary + Advanced Control Specialty Formulary": "Standard Opt Out Formulary + Advanced Control Specialty Formulary", "Value Formulary": "Value Formulary", "Advanced Control Choice Formulary": "Advanced Control Choice Formulary", "Standard Control Choice Formulary": "Standard Control Choice Formulary", "Standard Control Choice + Advanced Control Specialty Formulary": "Standard Control Choice + Advanced Control Specialty Formulary"}, "CMK-CoverBroaderVaccinationNetwork": {"No": "No", "Yes - Annual": "Yes - Annual", "Yes - Evergreen": "Yes - <PERSON><PERSON>"}, "CMK-IrsHdhpPreventativeList": {"None": "None", "HDHP Standard Generic Only with Brand Tier 1": "HDHP Standard Generic Only with Brand Tier 1", "HDHP Standard Modular Preventive Drug List": "HDHP Standard Modular Preventive Drug List", "Client Custom Drug List": "Client Custom Drug List", "HDHP Standard Preventive Drug List (Brand and Generic)": "HDHP Standard Preventive Drug List (Brand and Generic)", "HDHP Standard Generics Only": "HDHP Standard Generics Only"}, "CMK-UtilizationManagementBundle": {"Standard": "Standard", "Standard No Step Therapy": "Standard No Step Therapy"}, "CMK-StepTherapyOption": {"PGST": "PGST", "TGST": "TGST", "HPGST": "HPGST"}, "McapProductOptionsCmk": {"1": "<PERSON><PERSON> (Hospital Only)", "2": "True Accum + Copay Optimization", "3": "True Accum + PrudentRX", "4": "True Accums + PrudentRx HSA", "5": "True Accumulation"}, "McapProductOptionsEsi": {"1": "<PERSON><PERSON> (Hospital Only)", "2": "OOPP + SaveOnSP Adapt", "3": "OOPP + SaveOnSP Adapt no HIV", "4": "OOPP + SaveOnSP no HIV", "5": "OOPP + SaveOnSP Select no HIV", "6": "Out of Pocket Protection", "7": "Out of Pocket Protection + SaveOnSP", "8": "Out of Pocket Protection + SaveOnSP Select", "9": "Out of Pocket Protection + Variable Copay", "10": "SaveonSP", "11": "SaveOnSP Adapt", "12": "SaveOnSP Adapt no HIV", "13": "SaveOnSP only no HIV", "14": "SaveOnSP Select", "15": "SaveOnSP Select no HIV"}, "McapProductOptionsOpt": {"1": "<PERSON><PERSON> (Hospital Only)", "2": "In House CCAA on non-specialty", "3": "In House VCS custom list", "4": "In House VCS hard coded", "5": "In House VCS Transaction Based (standard approach)", "6": "CCAA + Variable Copay Solution Non-Specialty Only", "7": "CCAA + Variable Copay Solution Specialty & Non-Specialty", "8": "CCAA + Variable Copay Solution Specialty Only", "9": "Copay Card Accumlator Adjustment"}, "PvpProductOptionsEsi": {"0": "Do Not enroll", "1": "Enroll ACA only", "2": "Enroll ACA & Non ACA"}, "InternationalDrugSourcingProductOptions": {"1": "RxManage by GlobalRx", "2": "Other"}, "OptimizeMyCareProductOptions": {"1": "Diabetes Support", "2": "Full Condition Support"}, "PapProductOptions": {"1": "Amwins Rx", "2": "Impax Rx"}, "SaveOnSP Adapt": {}, "MandatoryMailGraceFills": {"0": "0", "1": "1", "2": "2", "3": "3", "N/A": "N/A"}, "RedList": {"Red List 1 & 2": "Red List 1 & 2", "Red List 1 & 3": "Red List 1 & 3", "Red List 1, 2, & 3": "Red List 1, 2, & 3", "Red List 2 & 3": "Red List 2 & 3", "Red List Tier 1": "Red List Tier 1", "Red List Tier 2": "Red List Tier 2", "Red List Tier 3": "Red List Tier 3"}}