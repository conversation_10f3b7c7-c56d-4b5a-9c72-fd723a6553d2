export const getProducts = async () => {
  const response = await fetch(
    `https://ilaz0xu075.execute-api.us-east-1.amazonaws.com/dev/products`
  ).then((response) => response.json());
  return response;
};

export const getProductById = async (productId: string) => {
  const response = await fetch(
    `https://ilaz0xu075.execute-api.us-east-1.amazonaws.com/dev/products/${productId}`
  ).then((response) => response.json());
  return response;
};

export const updateProduct = async (productId: string, productData: any) => {
  const response = await fetch(
    `https://ilaz0xu075.execute-api.us-east-1.amazonaws.com/dev/products/${productId}`,
    {
      body: JSON.stringify(productData),
      method: 'PUT',
    }
  );
  if (!response.ok) {
    const error = await response.json();
    throw error.details;
  }
  return response.json();
};

export const exportProducts = async (productData: any) => {
  const response = await fetch(
    `https://ilaz0xu075.execute-api.us-east-1.amazonaws.com/dev/export`,
    {
      body: JSON.stringify(productData),
      method: 'POST',
    }
  );
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.body || 'Failed to export product');
  }
  return response.json();
};
