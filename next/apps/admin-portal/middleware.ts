import {
  getSession,
  withMiddlewareAuthRequired,
} from '@auth0/nextjs-auth0/edge';
import { APPagePerms } from '@next/shared/constants';
import { decode } from '@tsndr/cloudflare-worker-jwt';
import { NextFetchEvent, NextRequest, NextResponse } from 'next/server';

const PUBLIC_PATHS = ['/api/health'];

const AUTH_ONLY_PATHS = ['/home', '/pharmacy-assurance'];

const authMiddleware = async (req: NextRequest, event: NextFetchEvent) => {
  const res = NextResponse.next();
  const user = await getSession(req, res);

  if (
    AUTH_ONLY_PATHS.includes(req.nextUrl.pathname) ||
    (req.nextUrl.pathname === '/pharmacy-assurance' && process.env.RXPA_ENABLED)
  ) {
    return res;
  }

  const splitStr = '{id}';
  const tokenKey = process.env.AUTH0_NAMESPACE_KEY + '/authorization-claims';
  const token = decode(user?.accessToken as string);
  // @ts-ignore
  const claims = token?.payload?.[tokenKey]?.claims;
  const permissions = claims?.permissions;

  // Check if path has explicit permissions
  const key = APPagePerms[req.nextUrl.pathname as keyof typeof APPagePerms];
  if (key) {
    if (key[0]) {
      const checkKey = key.filter((key: any) => permissions?.includes(key));
      if (!checkKey.length) {
        return NextResponse.redirect(new URL('/404', req.url));
      }
    }
  } else {
    // Dynamic route permission check
    const keys = Object.keys(APPagePerms) as Array<keyof typeof APPagePerms>;
    for (let i = 0; i < keys.length; i++) {
      const singleKey = keys[i];
      const pathname = req.nextUrl.pathname;
      if (!singleKey.includes(splitStr)) continue;

      const singleKeyArr = singleKey.split(splitStr);
      const filteredArr = singleKeyArr.filter((key: any) =>
        pathname.includes(key)
      );
      if (singleKeyArr.length !== filteredArr.length) {
        continue;
      }

      const checkKey = APPagePerms[singleKey].filter((key: any) =>
        permissions.includes(key)
      );
      if (!checkKey.length) {
        return NextResponse.redirect(new URL('/home', req.url));
      }
    }
  }

  return res;
};

export function middleware(req: NextRequest, event: NextFetchEvent) {
  if (PUBLIC_PATHS.includes(req.nextUrl.pathname)) {
    return NextResponse.next();
  }
  return withMiddlewareAuthRequired(authMiddleware)(req, event);
}
