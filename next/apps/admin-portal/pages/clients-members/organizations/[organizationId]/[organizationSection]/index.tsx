import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Tab,
  <PERSON>b<PERSON>ist,
  TabPanel,
  TabPanels,
  Tabs,
  Text,
} from '@chakra-ui/react';
import {
  MembersBase,
  OrganizationDashboard,
  OrgUsers,
  SkeletonHeader,
} from '@next/admin/components';
import { theme } from '@next/admin/constants';
import { useApi } from '@next/shared/api';
import { PermissionRoleContext } from '@next/shared/contexts';
import { formatDate } from '@next/shared/helpers';
import { useCustomText } from '@next/shared/hooks';
import { useParams, useRouter } from 'next/navigation';
import React, { useContext, useEffect, useState } from 'react';
import { AiOutlineShop } from 'react-icons/ai';
import { BsFillArrowLeftSquareFill } from 'react-icons/bs';

const OrganizationSection: React.FC = () => {
  const router = useRouter();
  const createCustomText = useCustomText();
  const params = useParams();
  const organizationId = params?.['organizationId'];
  const organizationSection = params?.['organizationSection'];

  const { canViewWithPerm } = useContext(PermissionRoleContext);

  const { organizationDetail } = useApi(
    ['organizationDetail'],
    {
      orgId: organizationId as string | number,
    },
    {
      onError: (err) => {
        if (err?.status === 403) {
          router.push('/404');
        }
      },
    }
  );

  const hasMember =
    canViewWithPerm('viewMembers') || canViewWithPerm('editMembers');
  const hasOrgUser = canViewWithPerm('viewOrganizationUsers');

  const getTabIndexFromSection = (
    section: string | string[] | undefined
  ): number => {
    if (section === 'employees' && hasMember) {
      return 1;
    }
    if (section === 'users' && hasOrgUser) {
      return hasMember ? 2 : 1;
    }
    return 0;
  };

  const [tabIndex, setTabIndex] = useState(
    getTabIndexFromSection(organizationSection)
  );

  useEffect(() => {
    const newTabIndex = getTabIndexFromSection(organizationSection);
    setTabIndex(newTabIndex);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [organizationSection, hasMember, hasOrgUser]);

  const handleTabsChange = (index: number) => {
    setTabIndex(index);

    const basePath = `/clients-members/organizations/${organizationId}`;
    let newPath = `${basePath}/dashboard`;

    if (index === 1) {
      if (hasMember) {
        newPath = `${basePath}/employees`;
      } else if (hasOrgUser) {
        newPath = `${basePath}/users`;
      }
    } else if (index === 2 && hasMember && hasOrgUser) {
      newPath = `${basePath}/users`;
    }

    router.push(newPath);
  };

  return (
    <>
      <Flex flexDirection={'column'} gap={8}>
        <Flex alignItems={'flex-start'} gap={8}>
          <Box
            background={theme.colors.brand.black}
            borderRadius={'5px'}
            onClick={() => router.back()}
            cursor={'pointer'}
          >
            <BsFillArrowLeftSquareFill
              size={30}
              color={theme.colors.brand.white}
            />
          </Box>
          {organizationDetail ? (
            <Flex flexDirection={'column'} gap={8}>
              <Flex alignItems={'center'} gap={10}>
                <AiOutlineShop
                  size={30}
                  style={{ margin: '0', padding: '0' }}
                  color={theme.colors.brand.grey}
                />
                <Heading fontWeight={500}>{organizationDetail.name}</Heading>
              </Flex>
              <Flex alignItems={'flex-start'} gap={8}>
                <Stack>
                  <Text
                    fontSize={12}
                    fontWeight={700}
                    textTransform={'uppercase'}
                    color={'gray'}
                  >
                    Type:
                  </Text>
                  {createCustomText({
                    type: organizationDetail?.type,
                    text: organizationDetail?.type,
                  })}
                </Stack>
                <Stack>
                  <Text
                    fontSize={12}
                    fontWeight={700}
                    textTransform={'uppercase'}
                    color={'gray'}
                  >
                    Status:
                  </Text>
                  {createCustomText({
                    processStatus: organizationDetail?.processStatus,
                    expirationDate: organizationDetail?.expirationDate || '',
                  })}
                </Stack>
                <Stack>
                  <Text
                    fontSize={12}
                    fontWeight={700}
                    textTransform={'uppercase'}
                    color={'gray'}
                  >
                    Original Effective Date:
                  </Text>
                  <p>
                    {organizationDetail?.effectiveDate
                      ? formatDate(organizationDetail?.effectiveDate)
                      : ''}
                  </p>
                </Stack>
              </Flex>
            </Flex>
          ) : (
            <SkeletonHeader />
          )}
        </Flex>
        <Tabs index={tabIndex} onChange={handleTabsChange}>
          <TabList marginBottom="30px">
            <Tab>Dashboard</Tab>
            {hasMember && <Tab>Members</Tab>}
            {hasOrgUser && <Tab>Users</Tab>}
          </TabList>

          <TabPanels background={theme.colors.brand.lightestgray} padding={0}>
            <TabPanel padding="0">
              <OrganizationDashboard
                organizationDetail={organizationDetail}
                organizationId={organizationId as string}
              />
            </TabPanel>
            {hasMember && (
              <TabPanel padding="0">
                <MembersBase orgName={organizationDetail?.name} />
              </TabPanel>
            )}
            {hasOrgUser && (
              <TabPanel padding="0">
                <OrgUsers />
              </TabPanel>
            )}
          </TabPanels>
        </Tabs>
      </Flex>
    </>
  );
};

export default OrganizationSection;
