import { Box, Button, Flex, Heading, Text } from '@chakra-ui/react';
import {
  CustomInputField,
  EligiblityImportFilters,
  EligiblityImportsHistoryTable,
} from '@next/admin/components';
import {
  adultDependent,
  defaultValues,
  disabledDependent,
  retainRetireeStatus,
  status,
  student,
  theme,
} from '@next/admin/constants';
import { useJavaApi } from '@next/shared/api';
import { importHistoryHelper } from '@next/shared/helpers';
import { useCustomToast, useQueryParams } from '@next/shared/hooks';
import { useParams } from 'next/navigation';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { IoMdArrowRoundBack } from 'react-icons/io';

const Filter = () => {
  const formInstance = useForm();
  const { handleSubmit, control, reset, watch } = formInstance;
  const router = useRouter();
  const showToast = useCustomToast();
  const [data, setData] = useState([]);
  const [buttonState, setButtonState] = useState(false);

  const params = useParams();
  const organizationId = params?.['organizationId'];

  const { page, size } = useQueryParams(['page', 'size']);

  const { eligibilityTermByAbsence, methodApi } = useJavaApi(
    ['eligibilityTermByAbsence'],
    {
      orgId: organizationId as string | number,
    }
  );

  useEffect(() => {
    const values = watch();
    methodApi('eligiblityImportHistory', {
      method: 'POST',
      restParams: {
        orgId: organizationId,
        pageSize: size || 10,
        pageNumber: page || 1,
      },
      data: { ...importHistoryHelper(values, organizationId as string) },
      onSuccess: (resp) => {
        setData(resp);
      },
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [page, size]);

  const onSubmit = (values: any) => {
    setButtonState(true);
    router.push(
      `/clients-members/organizations/${organizationId}/eligiblity-imports-history?pageSize=10&pageNumber=1`
    );
    methodApi('eligiblityImportHistory', {
      method: 'POST',
      restParams: {
        orgId: organizationId,
        pageSize: +page || 10,
        pageNumber: +size || 1,
      },
      data: { ...importHistoryHelper(values, organizationId as string) },
      onSuccess: (res) => {
        setData(res);
        setButtonState(false);
      },
      onError: (error) => {
        showToast({
          title: error?.data.message,
          status: 'error',
        });
        setButtonState(false);
      },
    });
  };
  useEffect(() => {
    reset(defaultValues);
    onSubmit(defaultValues);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleReset = () => {
    router.push(
      `/clients-members/organizations/${organizationId}/eligiblity-imports-history?pageSize=10&pageNumber=1`
    );
    reset(defaultValues);
    const values = watch();

    methodApi('eligiblityImportHistory', {
      method: 'POST',
      restParams: {
        orgId: organizationId,
        pageSize: 10,
        pageNumber: 1,
      },
      data: { ...importHistoryHelper(values, organizationId as string) },
      onSuccess: (res) => {
        setData(res);
      },
      onError: (error) => {
        showToast({
          title: error?.data.message,
          status: 'error',
        });
      },
    });
  };

  const termByAbsence =
    eligibilityTermByAbsence?.map((entry: Record<string, any>) => ({
      value: entry.termByAbsenceNo,
      label: entry.termByAbsence,
    })) || [];

  const homeUrl = `/clients-members/organizations/${params?.organizationId}/dashboard`;

  return (
    <Box>
      <Flex gap={2} alignItems={'center'}>
        <Button
          style={{ backgroundColor: `${theme.colors.brand.white}` }}
          onClick={() => router.push(homeUrl)}
          h={'25px'}
        >
          <IoMdArrowRoundBack style={{ fontSize: '25px' }} />
        </Button>

        <Heading size="sm" color="#4c6293">
          Eligibility Imports Filterable History{' '}
        </Heading>
      </Flex>

      <Box
        borderRadius="md"
        padding={'10px 15px'}
        boxShadow={'0px 0px 45px #0000000F'}
        backgroundColor={'#ffffff'}
        mt={4}
      >
        <FormProvider {...formInstance}>
          <form onSubmit={handleSubmit(onSubmit)}>
            <EligiblityImportFilters />

            <Box display={'flex'} gap={'155px'}>
              <CustomInputField
                componentType="select"
                controlprop={control}
                options={status}
              />

              <CustomInputField
                componentType="select"
                controlprop={control}
                options={retainRetireeStatus}
              />
            </Box>

            <Box display={'flex'} flexDirection={'column'}>
              <Box display={'flex'} gap={'150px'} alignItems={'baseline'}>
                <Box alignItems="center">
                  <CustomInputField
                    componentType="select"
                    controlprop={control}
                    options={{
                      name: 'termByAbsence',
                      label: 'Term by Absence',
                      options: [
                        { value: 'All', label: 'All' },
                        ...(termByAbsence || []),
                      ],
                      width: '450px',
                    }}
                  />
                </Box>

                <Box
                  width={'50vw'}
                  display={'flex'}
                  flexDir={'column'}
                  justifyContent={'space-between'}
                >
                  <Text
                    fontSize={12}
                    whiteSpace={'nowrap'}
                    marginBottom={'5px'}
                    fontWeight={'bold'}
                    mb={2}
                  >
                    Retain Dependent Relationship Status:
                  </Text>
                  <Box display={'flex'} justifyContent={'space-between'}>
                    <CustomInputField
                      componentType="select"
                      controlprop={control}
                      options={student}
                    />
                    <CustomInputField
                      componentType="select"
                      controlprop={control}
                      options={adultDependent}
                    />
                    <CustomInputField
                      componentType="select"
                      controlprop={control}
                      options={disabledDependent}
                    />
                  </Box>
                </Box>
              </Box>
            </Box>

            <Button type="submit" colorScheme="blue" isDisabled={buttonState}>
              Apply
            </Button>
            <Button m={2} colorScheme="blue" onClick={() => handleReset()}>
              Reset
            </Button>
          </form>
        </FormProvider>
      </Box>
      <EligiblityImportsHistoryTable data={data} />
    </Box>
  );
};

export default Filter;
