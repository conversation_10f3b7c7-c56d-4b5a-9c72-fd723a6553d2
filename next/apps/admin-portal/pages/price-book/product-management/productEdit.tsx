import { useUser } from '@auth0/nextjs-auth0/client';
import {
  Alert,
  AlertIcon,
  Box,
  Button,
  Card,
  CardBody,
  CardHeader,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Grid,
  GridItem,
  Heading,
  InputGroup,
  InputRightAddon,
  Link,
  NumberInput,
  NumberInputField,
  SimpleGrid,
  Skeleton,
  Tab,
  TabList,
  TabPanel,
  TabPanels,
  Tabs,
  Text,
  useToast,
  VStack,
} from '@chakra-ui/react';
import {
  getProductById,
  updateProduct,
} from 'apps/admin-portal/components/pricing/lib/services/productService';
import {
  ProductData as Product,
  ProductValues,
} from 'apps/admin-portal/components/pricing/lib/types/productManagement';
import { formatDate } from 'apps/admin-portal/components/pricing/lib/utils/formatters';
import { channelSections } from 'apps/admin-portal/components/pricing/lib/utils/productManagementConstants';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';

// Pricing component for each channel and drug type
interface PricingFieldProps {
  label: string;
  value: null | undefined | Record<string, any>;
  channel: string;
  onChange: (
    channel: string,
    drugType: 'brand' | 'generic' | 'ldd' | 'nonLdd',
    field: 'discount' | 'dispensingFee' | 'rebate',
    value: number | null | string
  ) => void;
  includeRebate?: boolean;
  error?: string;
  isReadOnly?: boolean;
  drugType: 'brand' | 'generic' | 'ldd' | 'nonLdd';
}

const PricingField: React.FC<PricingFieldProps> = ({
  label,
  value,
  channel,
  onChange,
  includeRebate = false,
  error,
  isReadOnly = false,
  drugType,
}) => {
  const handleChange =
    (field: 'discount' | 'dispensingFee' | 'rebate') =>
    (valueAsString: string, valueAsNumber: number) => {
      if (/^\d*\.?\d{0,4}$/.test(valueAsString)) {
        if (valueAsString === '') onChange(channel, drugType, field, null);
        else onChange(channel, drugType, field, valueAsString);
      }
    };

  return (
    <FormControl>
      <FormLabel fontSize="sm">{label}</FormLabel>
      <VStack spacing={3} align="start">
        <FormControl isInvalid={!!error}>
          <InputGroup size="sm">
            <NumberInput
              min={0}
              max={100}
              value={value?.discount ?? undefined}
              onChange={handleChange('discount')}
              isReadOnly={isReadOnly}
            >
              <NumberInputField placeholder="Discount" />
              <InputRightAddon>%</InputRightAddon>
            </NumberInput>
          </InputGroup>
          {error && <FormErrorMessage>{error}</FormErrorMessage>}
        </FormControl>

        <InputGroup size="sm">
          <NumberInput
            min={0}
            onChange={handleChange('dispensingFee')}
            value={value?.dispensingFee ?? undefined}
            isReadOnly={isReadOnly}
          >
            <NumberInputField placeholder="Dispensing Fee" />
            <InputRightAddon>$</InputRightAddon>
          </NumberInput>
        </InputGroup>

        {includeRebate && (
          <InputGroup size="sm">
            <NumberInput
              min={0}
              value={value?.rebate ?? undefined}
              onChange={handleChange('rebate')}
              isReadOnly={isReadOnly}
            >
              <NumberInputField placeholder="Rebate" />
              <InputRightAddon>$</InputRightAddon>
            </NumberInput>
          </InputGroup>
        )}
      </VStack>
    </FormControl>
  );
};

const ProductEdit = () => {
  const searchParams = useSearchParams();
  const productId = searchParams?.get('productId') as string;
  const router = useRouter();
  const toast = useToast();
  const { user } = useUser();

  const [product, setProduct] = useState<Product | null>(null);
  const [productValues, setProductValues] = useState<ProductValues | null>(
    null
  );
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [activeTab, setActiveTab] = useState(0);
  const [touchedValues, setTouchedValues] = useState<Record<string, boolean>>(
    {}
  );

  const transformValues = (obj: any): any => {
    return Object.fromEntries(
      Object.entries(obj).map(([key, value]) => {
        if (value && typeof value === 'object' && !Array.isArray(value)) {
          return [key, transformValues(value)];
        }
        if (key === 'discount' && typeof value === 'number' && value <= 1) {
          return [key, Number((value * 100).toFixed(4))];
        }
        return [key, value];
      })
    );
  };

  // Fetch product data
  useEffect(() => {
    const fetchProduct = async () => {
      if (!productId) return;

      try {
        setIsLoading(true);
        const data = await getProductById(productId);
        setProduct(data);
        setProductValues(
          transformValues(JSON.parse(JSON.stringify(data.values)))
        );
      } catch (err: any) {
        setError(
          err.response?.data?.message || 'Failed to load product details'
        );

        toast({
          title: 'Error loading product',
          description:
            err.response?.data?.message ||
            'An error occurred while loading product.',
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchProduct();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [productId, toast]);

  // Handle form field changes for pricing values
  const handlePricingChange = (
    channel: string,
    drugType: 'brand' | 'generic' | 'ldd' | 'nonLdd',
    field: 'discount' | 'dispensingFee' | 'rebate',
    value: number | null | string
  ) => {
    if (!productValues) return;

    setProductValues((prevValues: ProductValues | null) => {
      if (!prevValues) return null;

      const updatedValues = { ...prevValues };

      // Ensure the channel exists
      if (!updatedValues[channel]) {
        if (drugType === 'ldd' || drugType === 'nonLdd') {
          updatedValues[channel] = {
            ldd: { discount: 0, dispensingFee: null, rebate: null },
            nonLdd: { discount: 0, dispensingFee: null },
          };
        } else {
          updatedValues[channel] = {
            brand: { discount: 0, dispensingFee: null, rebate: null },
            generic: { discount: 0, dispensingFee: null },
          };
        }
      }

      // Update the specific field
      if (updatedValues[channel] && updatedValues[channel][drugType]) {
        updatedValues[channel][drugType] = {
          ...updatedValues[channel][drugType],
          [field]: value,
        };
      }

      return updatedValues;
    });
    setTouchedValues((prev) => ({
      ...prev,
      [`${channel}.${drugType}.${field}`]: true,
    }));
  };

  const getUpdatedData = () => {
    // Prepare update data
    // Get current date - ensure we're working with noon to avoid timezone issues
    const today = new Date();
    today.setHours(12, 0, 0, 0);

    // Set yesterday as expiry date for the current version
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    yesterday.setHours(12, 0, 0, 0);

    const updatedProductValues = structuredClone(productValues);
    Object.keys(touchedValues).map((path) => {
      let current = updatedProductValues;
      const keys = path.split('.');
      for (let i = 0; i < keys.length - 1; i++) {
        current = current?.[keys[i]];
      }
      if (current) {
        const lastKey = keys[keys.length - 1];
        current[lastKey] =
          lastKey === 'discount'
            ? parseFloat(current[lastKey]) / 100
            : parseFloat(current[lastKey]);
      }
    });

    const updateData = {
      // expiry date for product history
      expiryDate: product?.metadata?.expiryDate,

      // new version to update the product
      newVersion: {
        effectiveDate: today.toISOString().split('T')[0],
        values: updatedProductValues,
        // Keep the same parameters
        parameters: product?.parameters,
        createdBy: user?.email,
      },
    };
    return updateData;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!productId || !product || !productValues) {
      toast({
        title: 'Error',
        description: 'Missing product information',
        status: 'error',
        duration: 3000,
      });
      return;
    }

    // Form validation
    let validationErrors: Record<string, string> = {};

    // Add validation here if needed

    Object.entries(productValues).map(([key, value]) => {
      //special handling for blendedSpecialty
      if (key === 'blendedSpecialty') {
        if (value.ldd?.discount && Number(value.ldd?.discount) > 100) {
          validationErrors = {
            ...validationErrors,
            [`blendedSpecialty.ldd.discount`]:
              'discount should be less than 100',
          };
        }
        if (value.nonLdd?.discount && Number(value.nonLdd?.discount) > 100) {
          validationErrors = {
            ...validationErrors,
            [`blendedSpecialty.nonLdd.discount`]:
              'discount should be less than 100',
          };
        }
      } else if (key === 'overallFeeAndCredit') {
        return;
      } else {
        if (value.brand?.discount && Number(value.brand?.discount) > 100) {
          validationErrors = {
            ...validationErrors,
            [`${key}.brand.discount`]: 'discount should be less than 100',
          };
        }
        if (value.generic?.discount && Number(value.generic?.discount) > 100) {
          validationErrors = {
            ...validationErrors,
            [`${key}.generic.discount`]: 'discount should be less than 100',
          };
        }
      }
    });

    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      toast({
        title: 'Validation Error',
        description: 'Please correct the errors in the form across all tabs.',
        status: 'error',
        duration: 3000,
      });
      return;
    }
    const updateData = getUpdatedData();

    try {
      setIsSaving(true);
      await updateProduct(productId, updateData);

      toast({
        title: 'Success',
        description: 'Product updated successfully',
        status: 'success',
        duration: 3000,
      });

      // Navigate back to product details
      router.push(
        `/price-book/product-management/productDetails?productId=${productId}`
      );
    } catch (err: any) {
      toast({
        title: 'Error updating product',
        description:
          err.reason === 'duplicate_price_record' ? (
            <>
              <Text>
                The price record you are trying to create matches a previous
                record. Duplicate record will not be created. You can review
                record with the Effective Date of{' '}
                {new Intl.DateTimeFormat('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                }).format(new Date(err.effectiveDate))}{' '}
                here.{' '}
              </Text>
              <Link
                color="#63b3ed"
                href="#"
                fontWeight="medium"
                display="inline-block"
                onClick={() =>
                  router.push(
                    `/price-book/product-management/productDetails?productId=${err.productId}`
                  )
                }
              >
                {err.productName}
              </Link>
            </>
          ) : (
            'An error occurred while updating product. Please try again later.'
          ),
        status: 'error',
        duration: err.reason === 'duplicate_price_record' ? null : 5000,
        isClosable: true,
      });
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <Box p={4}>
        <Skeleton height="40px" mb={6} />
        <Skeleton height="200px" mb={6} />
        <Skeleton height="300px" />
      </Box>
    );
  }

  if (error || !product || !productValues) {
    return (
      <Alert status="error" borderRadius="md">
        <AlertIcon />
        {error || 'Failed to load product details'}
      </Alert>
    );
  }

  return (
    <Box as="form" onSubmit={handleSubmit} p={4}>
      <Heading size="lg" mb={6}>
        Edit Product
      </Heading>

      {/* Product Information Card */}
      <Card mb={6}>
        <CardHeader>
          <Heading size="md">Product Information</Heading>
        </CardHeader>
        <CardBody>
          <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={4}>
            <Box>
              <Text fontWeight="bold" fontSize="sm" color="gray.500">
                PRODUCT NAME
              </Text>
              <Text fontWeight="medium">
                {product.metadata.productName || 'Unnamed Product'}
              </Text>
            </Box>

            <Box>
              <Text fontWeight="bold" fontSize="sm" color="gray.500">
                PBM
              </Text>
              <Text>{product.parameters.pbm}</Text>
            </Box>

            <Box>
              <Text fontWeight="bold" fontSize="sm" color="gray.500">
                EFFECTIVE DATES
              </Text>
              <Text>
                {formatDate(product.metadata.effectiveDate)} -{' '}
                {product.metadata.expiryDate
                  ? formatDate(product.metadata.expiryDate)
                  : 'Present'}
              </Text>
            </Box>

            <Box>
              <Text fontWeight="bold" fontSize="sm" color="gray.500">
                DISCOUNT TYPE
              </Text>
              <Text>{product.parameters.discount_rebate_type}</Text>
            </Box>
          </SimpleGrid>

          <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={4} mt={4}>
            <Box>
              <Text fontWeight="bold" fontSize="sm" color="gray.500">
                CLIENT SIZE
              </Text>
              <Text>{product.parameters.client_size}</Text>
            </Box>

            <Box>
              <Text fontWeight="bold" fontSize="sm" color="gray.500">
                FORMULARY
              </Text>
              <Text>{product.parameters.formulary}</Text>
            </Box>
          </SimpleGrid>
        </CardBody>
      </Card>

      {/* Values Editing Section */}
      <Card>
        <CardHeader>
          <Heading size="md">Product Values</Heading>
          <Text fontSize="sm" color="gray.600" mt={1}>
            Edit the product values below. The parameters are shown for
            reference but cannot be edited.
          </Text>
        </CardHeader>
        <CardBody>
          <Tabs isLazy index={activeTab} onChange={setActiveTab}>
            <TabList>
              <Tab>Retail</Tab>
              <Tab>Mail</Tab>
              <Tab>Specialty</Tab>
              <Tab>Fees & Credits</Tab>
            </TabList>

            <TabPanels>
              {/* Retail Tab */}
              <TabPanel>
                <Grid
                  templateColumns={{ base: '1fr', md: 'repeat(2, 1fr)' }}
                  gap={6}
                >
                  {channelSections.slice(0, 3).map((channel) => (
                    <GridItem key={channel.key}>
                      <Box borderWidth="1px" borderRadius="md" p={4}>
                        <Heading size="sm" mb={4}>
                          {channel.name}
                        </Heading>

                        <SimpleGrid columns={2} spacing={4}>
                          {channel.hasBrand && (
                            <PricingField
                              label="Brand"
                              channel={channel.key}
                              drugType="brand"
                              value={productValues[channel.key]?.brand}
                              onChange={handlePricingChange}
                              includeRebate={channel.hasRebate}
                              error={errors[`${channel.key}.brand.discount`]}
                            />
                          )}

                          {channel.hasGeneric && (
                            <PricingField
                              label="Generic"
                              value={productValues[channel.key]?.generic}
                              channel={channel.key}
                              drugType="generic"
                              onChange={handlePricingChange}
                              error={errors[`${channel.key}.generic.discount`]}
                            />
                          )}
                        </SimpleGrid>
                      </Box>
                    </GridItem>
                  ))}
                </Grid>
              </TabPanel>

              {/* Mail Tab */}
              <TabPanel>
                <Grid
                  templateColumns={{ base: '1fr', md: 'repeat(2, 1fr)' }}
                  gap={6}
                >
                  {channelSections.slice(3, 4).map((channel) => (
                    <GridItem key={channel.key}>
                      <Box borderWidth="1px" borderRadius="md" p={4}>
                        <Heading size="sm" mb={4}>
                          {channel.name}
                        </Heading>

                        <SimpleGrid columns={2} spacing={4}>
                          {channel.hasBrand && (
                            <PricingField
                              label="Brand"
                              value={productValues[channel.key]?.brand}
                              channel={channel.key}
                              drugType="brand"
                              onChange={handlePricingChange}
                              includeRebate={channel.hasRebate}
                              error={errors[`${channel.key}.brand.discount`]}
                            />
                          )}

                          {channel.hasGeneric && (
                            <PricingField
                              label="Generic"
                              value={productValues[channel.key]?.generic}
                              channel={channel.key}
                              drugType="generic"
                              onChange={handlePricingChange}
                              error={errors[`${channel.key}.generic.discount`]}
                            />
                          )}
                        </SimpleGrid>
                      </Box>
                    </GridItem>
                  ))}
                </Grid>
              </TabPanel>

              {/* Specialty Tab */}
              <TabPanel>
                <Grid
                  templateColumns={{ base: '1fr', md: 'repeat(2, 1fr)' }}
                  gap={6}
                >
                  {channelSections.slice(4, 8).map((channel) => (
                    <GridItem key={channel.key}>
                      <Box borderWidth="1px" borderRadius="md" p={4}>
                        <Heading size="sm" mb={4}>
                          {channel.name}
                        </Heading>

                        <SimpleGrid columns={2} spacing={4}>
                          {channel.hasBrand && (
                            <PricingField
                              label="Brand"
                              value={productValues[channel.key]?.brand}
                              channel={channel.key}
                              drugType="brand"
                              onChange={handlePricingChange}
                              includeRebate={channel.hasRebate}
                              error={errors[`${channel.key}.brand.discount`]}
                            />
                          )}

                          {channel.hasGeneric && (
                            <PricingField
                              label="Generic"
                              value={productValues[channel.key]?.generic}
                              channel={channel.key}
                              drugType="generic"
                              onChange={handlePricingChange}
                              error={errors[`${channel.key}.generic.discount`]}
                            />
                          )}
                        </SimpleGrid>
                      </Box>
                    </GridItem>
                  ))}
                  {channelSections.slice(8, 9).map((channel) => (
                    <GridItem key={channel.key}>
                      <Box borderWidth="1px" borderRadius="md" p={4}>
                        <Heading size="sm" mb={4}>
                          {channel.name}
                        </Heading>

                        <SimpleGrid columns={2} spacing={4}>
                          {channel.hasBrand && (
                            <PricingField
                              label="LDD"
                              value={productValues[channel.key]?.ldd}
                              channel={channel.key}
                              drugType="ldd"
                              onChange={handlePricingChange}
                              includeRebate={channel.hasRebate}
                              error={errors[`${channel.key}.ldd.discount`]}
                            />
                          )}

                          {channel.hasGeneric && (
                            <PricingField
                              label="Non-LDD"
                              value={productValues[channel.key]?.nonLdd}
                              channel={channel.key}
                              drugType="nonLdd"
                              onChange={handlePricingChange}
                              includeRebate={channel.hasRebate}
                              error={errors[`${channel.key}.nonLdd.discount`]}
                            />
                          )}
                        </SimpleGrid>
                      </Box>
                    </GridItem>
                  ))}
                </Grid>
              </TabPanel>

              {/* Fees & Credits Tab */}
              <TabPanel>
                <Box borderWidth="1px" borderRadius="md" p={4}>
                  <Heading size="sm" mb={4}>
                    Overall Fee and Credit
                  </Heading>

                  <SimpleGrid columns={{ base: 1, md: 3 }} spacing={4}>
                    <FormControl>
                      <FormLabel fontSize="sm">PEPM Rebate Credit</FormLabel>
                      <InputGroup>
                        <NumberInput
                          min={0}
                          value={
                            productValues.overallFeeAndCredit
                              ?.pepmRebateCredit ?? undefined
                          }
                          onChange={(valueAsString, valueAsNumber) => {
                            if (/^\d*\.?\d{0,4}$/.test(valueAsString)) {
                              setProductValues((prev) => {
                                if (!prev) return null;
                                return {
                                  ...prev,
                                  overallFeeAndCredit: {
                                    ...prev.overallFeeAndCredit,
                                    pepmRebateCredit: valueAsString ?? null,
                                  },
                                };
                              });
                              setTouchedValues((prev) => ({
                                ...prev,
                                [`overallFeeAndCredit.pepmRebateCredit`]: true,
                              }));
                            }
                          }}
                        >
                          <NumberInputField />
                          <InputRightAddon>$</InputRightAddon>
                        </NumberInput>
                      </InputGroup>
                    </FormControl>

                    <FormControl>
                      <FormLabel fontSize="sm">Pricing Fee</FormLabel>
                      <InputGroup>
                        <NumberInput
                          min={0}
                          value={
                            productValues.overallFeeAndCredit?.pricingFee ??
                            undefined
                          }
                          onChange={(valueAsString, valueAsNumber) => {
                            if (/^\d*\.?\d{0,4}$/.test(valueAsString)) {
                              setProductValues((prev: ProductValues | null) => {
                                if (!prev) return null;
                                return {
                                  ...prev,
                                  overallFeeAndCredit: {
                                    ...prev.overallFeeAndCredit,
                                    pricingFee: valueAsString ?? null,
                                  },
                                };
                              });
                              setTouchedValues((prev) => ({
                                ...prev,
                                [`overallFeeAndCredit.pricingFee`]: true,
                              }));
                            }
                          }}
                        >
                          <NumberInputField />
                          <InputRightAddon>$</InputRightAddon>
                        </NumberInput>
                      </InputGroup>
                    </FormControl>

                    <FormControl>
                      <FormLabel fontSize="sm">In-House Pharmacy Fee</FormLabel>
                      <InputGroup>
                        <NumberInput
                          min={0}
                          value={
                            productValues.overallFeeAndCredit
                              ?.inHousePharmacyFee ?? undefined
                          }
                          onChange={(valueAsString, valueAsNumber) => {
                            if (/^\d*\.?\d{0,4}$/.test(valueAsString)) {
                              setProductValues((prev: ProductValues | null) => {
                                if (!prev) return null;
                                return {
                                  ...prev,
                                  overallFeeAndCredit: {
                                    ...prev.overallFeeAndCredit,
                                    inHousePharmacyFee: valueAsString ?? null,
                                  },
                                };
                              });
                              setTouchedValues((prev) => ({
                                ...prev,
                                [`overallFeeAndCredit.inHousePharmacyFee`]:
                                  true,
                              }));
                            }
                          }}
                        >
                          <NumberInputField />
                          <InputRightAddon>$</InputRightAddon>
                        </NumberInput>
                      </InputGroup>
                    </FormControl>
                  </SimpleGrid>
                </Box>
              </TabPanel>
            </TabPanels>
          </Tabs>
        </CardBody>
      </Card>

      {/* Form Actions */}
      <Flex justify="flex-end" mt={6} gap={4}>
        <Button
          variant="outline"
          onClick={() =>
            router.push(
              `/price-book/product-management/productDetails?productId=${productId}`
            )
          }
          isDisabled={isSaving}
        >
          Cancel
        </Button>

        <Button
          colorScheme="blue"
          type="submit"
          isLoading={isSaving}
          loadingText="Saving..."
          isDisabled={Object.keys(touchedValues).length === 0}
        >
          Save Changes
        </Button>
      </Flex>
    </Box>
  );
};

export default ProductEdit;
