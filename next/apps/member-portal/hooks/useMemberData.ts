import { useUser } from '@auth0/nextjs-auth0/client';
import { AdminUserContext } from '@next/shared/contexts';
import { useContext } from 'react';

import { useImpersonationStore } from '../stores/useImpersonationStore';

export const useMemberData = () => {
  const { user } = useUser();
  const { userState } = useContext(AdminUserContext);
  const { impMember } = useImpersonationStore();

  const isAdmin = user?.['https://rxbenefits.com/isAdmin'] === true;

  const dataSource =
    isAdmin && impMember ? impMember : userState?.metaData?.data?.[0];

  const isDependent = dataSource?.dependentNo !== null;

  const memberData = {
    firstName: dataSource?.firstName,
    lastName: dataSource?.lastName,
    middleInitial: dataSource?.middleInitial,
    birthdate: dataSource?.birthdate,
    dependentNo: dataSource?.dependentNo,
    employeeNo: dataSource?.employeeNo,
    organizationNo: dataSource?.organizationNo,
    alternateId: dataSource?.alternateId,
    email: dataSource?.email,
    isDependent,
    isAdmin,
    impMember,
    dependents:
      isAdmin && impMember
        ? impMember?.dependents ?? (dataSource?.dependentNo ? [] : undefined)
        : undefined,
  };

  return memberData;
};
