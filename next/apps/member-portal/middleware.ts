import { getSession } from '@auth0/nextjs-auth0/edge';
import { APPagePerms } from '@next/shared/constants';
import { decode } from '@tsndr/cloudflare-worker-jwt';
import { NextRequest, NextResponse } from 'next/server';

const ignoredPaths = ['/api/health'];

export function middleware(request: NextRequest) {
  // If the path is in the ignoredPaths list, allow the request to continue
  if (ignoredPaths.includes(request.nextUrl.pathname)) {
    return NextResponse.next();
  } else {
    // If the path is under /admin, enforce the protection logic
    if (request.nextUrl.pathname.startsWith('/admin/')) {
      const res = NextResponse.next(); // Create a NextResponse object to pass as the second argument

      // Pass both request and response to getSession as separate arguments
      return getSession(request, res).then(async (session) => {
        // If no session or user, redirect to login
        if (!session || !session.user) {
          return NextResponse.redirect(new URL('/CheckUserLogin', request.url));
        }

        if (!session.user['https://rxbenefits.com/isAdmin']) {
          return NextResponse.redirect(new URL('/home', request.url));
        }
        const tokenKey =
          process.env.AUTH0_NAMESPACE_KEY + '/authorization-claims';
        const token = decode(session.accessToken as string);

        const claims = (token?.payload as Record<string, any>)?.[tokenKey]
          ?.claims;
        const permissions = claims?.permissions;

        const pathPermissions =
          APPagePerms[request.nextUrl.pathname as keyof typeof APPagePerms];

        if (pathPermissions) {
          const checkKey = pathPermissions.filter((key: string) =>
            permissions.includes(key)
          );
          if (!checkKey.length) {
            return NextResponse.redirect(new URL('/home', request.url));
          }
        }

        return res;
      });
    }

    return NextResponse.next();
  }
}

export const config = {
  matcher: ['/admin/:path*'], // Correct matcher for all paths starting with /admin/
};
