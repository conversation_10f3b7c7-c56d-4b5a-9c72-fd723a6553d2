import { useUser } from '@auth0/nextjs-auth0/client';
import { Box, Flex, Image, Spinner, Text } from '@chakra-ui/react';
import { AdminUserContext } from '@next/shared/contexts';
import { useRouter } from 'next/navigation';
import { useContext, useEffect, useState } from 'react';

/* eslint-disable-next-line */
export interface HomeProps {}

export function SetUserLogin(props: HomeProps) {
  const router = useRouter();
  const { user, isLoading } = useUser();
  const { userState } = useContext(AdminUserContext);
  const [loading, setLoading] = useState(true); // Loading state to prevent rendering content before the check

  useEffect(() => {
    if (user && user['https://rxbenefits.com/isAdmin'] === true) {
      sessionStorage.setItem('logged', 'true');
      router.push('/admin/impersonation-tool');
      return;
    }
    if (user && user?.email_verified) {
      sessionStorage.setItem('logged', 'true');
      router.push('/home');
    } else if (!isLoading) {
      setLoading(false);
    }
  }, [user, isLoading, router]);

  if (loading) {
    return (
      <Flex minH="100vh" justifyContent="center" alignItems="center" bg="white">
        <Spinner size="xl" />
      </Flex>
    );
  }
  return (
    <>
      <Box
        bg="white"
        position="absolute"
        minH="100vh"
        left={0}
        right={0}
        top={0}
        pt={20}
        px={4}
      >
        <Box
          position="relative"
          borderRadius="36px" // Rounded corners for the gradient border
          bg="transparent" // Ensure background is transparent to see the gradient
          padding="5px" // Adjust padding to control the border width (thinner)
          overflow="hidden" // Ensure content doesn't overflow the rounded border
          zIndex="9999"
          maxW="690px"
          m="auto"
        >
          {/* Gradient Border */}
          <Box
            position="absolute"
            top="0"
            left="0"
            right="0"
            bottom="0"
            borderRadius="30px" // Border radius to match the parent
            background="linear-gradient(to right, #6CB203, #00A361)" // Smooth gradient
            zIndex="-1" // Behind the content box
          />

          {/* Content Box */}
          <Box
            height="100%"
            width="100%"
            borderRadius="30px" // Content box rounded corners
            bg="white" // Background color for the content box
            textAlign="center"
            pt={4}
            pb={8}
            px={4}
          >
            <Flex alignItems={'center'} flexDirection={'column'}>
              <Image
                maxWidth={'110px'}
                alt={'logo'}
                src="https://static-assets.rxbenefits.cloud/images/rxbenefits_sm_logo.svg"
              />
              <Text fontSize="2xl" fontWeight="extrabold" pb={1}>
                Check your Email
              </Text>
              <Text fontSize="14px" color="gray.600">
                Confirm your email address by clicking the link in the email we
                sent to {userState?.email}.
              </Text>
            </Flex>
          </Box>
        </Box>
      </Box>
      <Box bg="white" minH="60vh"></Box>
    </>
  );
}

export default SetUserLogin;
