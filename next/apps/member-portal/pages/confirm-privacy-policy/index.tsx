import { Box, Button, Checkbox, Heading, Text } from '@chakra-ui/react';
import { useApi } from '@next/shared/api';
import PrivacyPolicyIcon from 'apps/member-portal/components/CustomIcons/PrivacyPolicyIcon';
import { useMemberData } from 'apps/member-portal/hooks/useMemberData';
import { useImpersonationStore } from 'apps/member-portal/stores/useImpersonationStore';
import { useOnboardingStore } from 'apps/member-portal/stores/useOnboardingStore';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { FaArrowUpRightFromSquare } from 'react-icons/fa6';
const ConfirmPrivacyPolicy = () => {
  const router = useRouter();
  const { methodApi } = useApi();
  const [termsAccepted, setTermsAccepted] = useState(false);
  const {
    firstName,
    lastName,
    organizationNo,
    employeeNo,
    dependentNo,
    isDependent,
    isAdmin,
  } = useMemberData();

  const { resetOnboardingState } = useOnboardingStore.getState();
  const { setImpPrivacyAcknowledgedComplete } = useImpersonationStore();

  useEffect(() => {
    if (organizationNo && employeeNo) {
      methodApi('memberGetCommunicationPreferences', {
        method: 'POST',
        body: {
          organizationNo,
          employeeNo,
          dependentNo,
        },
        onSuccess: (res) => {
          console.log(res);
        },
        onError: () => {
          console.warn('Failed to fetch communication preferences');
        },
      });
    }
  }, [organizationNo, employeeNo, dependentNo, methodApi]);

  const logout = () => {
    router.push('/api/auth/logout');
  };

  const handleUpdate = () => {
    if (isAdmin) {
      setImpPrivacyAcknowledgedComplete(true);
      router.push('/home');
      return;
    }

    if (!organizationNo || !employeeNo) return;

    methodApi('memberGetCommunicationPreferences', {
      method: 'POST',
      body: {
        organizationNo,
        employeeNo,
        dependentNo,
      },
      onSuccess: (res) => {
        const currentPrefs = res.data;

        const updatedPrefs = {
          eventName: 'update',
          source: 'MemberPortal',
          memberId: 'yes',
          firstName,
          lastName,
          organizationNo,
          employeeNo,
          dependentNo,
          updatedBy: isDependent
            ? dependentNo.toString()
            : employeeNo.toString(),
          emailAddress: currentPrefs.emailAddress,
          phoneNumber: currentPrefs.phoneNumber,
          commsPref: currentPrefs.commsPref,
          privacypolicyacknowledgeddate: new Date(),
        };

        methodApi('memberUpdateCommunicationPreferences', {
          method: 'POST',
          body: updatedPrefs,
          onSuccess: () => {
            resetOnboardingState();
            router.push('/home');
          },
          onError: () => {
            console.log('Error updating communication preferences');
          },
        });
      },
      onError: () => {
        console.warn('Failed to fetch current communication preferences');
      },
    });
  };

  return (
    <>
      <Box
        bg="white"
        position="absolute"
        minH="100vh"
        left={0}
        right={0}
        top={0}
        pt={40}
        px={4}
      >
        <Box
          display="flex"
          justifyContent="center"
          alignItems="center"
          color="gray.800"
          padding="40px"
        >
          <PrivacyPolicyIcon />
        </Box>

        <Box textAlign="center" color="gray.800">
          <Heading>
            <Text fontSize="18px">Review and Accept Terms</Text>
          </Heading>
        </Box>

        <Box textAlign="center" mt={6} color="gray.800">
          <Text>
            Before continuing, you must review and agree to <br /> the
            RxBenefits{' '}
            <span style={{ display: 'inline-block', verticalAlign: 'middle' }}>
              <FaArrowUpRightFromSquare />
            </span>{' '}
            <a
              href="https://www.rxbenefits.com/privacy-policy/terms-of-use/"
              target="_blank"
              rel="noopener noreferrer"
              style={{ textDecoration: 'underline' }}
            >
              Terms of Use
            </a>{' '}
            and{' '}
            <span style={{ display: 'inline-block', verticalAlign: 'middle' }}>
              <FaArrowUpRightFromSquare />
            </span>{' '}
            <a
              href="https://www.rxbenefits.com/privacy-policy/"
              target="_blank"
              rel="noopener noreferrer"
              style={{ textDecoration: 'underline' }}
            >
              Privacy Policy
            </a>
            .
          </Text>
        </Box>

        <Box maxW="480px" mx="auto" py={6} bg="white" color="gray.800">
          <Box p={2}>
            <Checkbox
              size="md"
              onChange={() => setTermsAccepted(!termsAccepted)}
            >
              I have read and agree to the Terms of Use and Privacy Policy
            </Checkbox>
          </Box>
          <Box
            display="flex"
            justifyContent={['flex-start', 'flex-end']}
            mt={4}
            minH="50vh"
            flexDirection={['column', 'row']}
            gap={2}
          >
            <Button bg="none" onClick={logout}>
              Decline and Logout
            </Button>
            <Button
              variant="solid"
              colorScheme="green"
              w={['full', 'auto']}
              isDisabled={!termsAccepted}
              onClick={handleUpdate}
            >
              Accept and Continue
            </Button>
          </Box>
        </Box>
      </Box>
      <Box bg="white" minH="90vh" />
    </>
  );
};

export default ConfirmPrivacyPolicy;
