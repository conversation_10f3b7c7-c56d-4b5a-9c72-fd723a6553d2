import {
  Box,
  Button,
  Checkbox,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Heading,
  HStack,
  Input,
  Stack,
  Text,
} from '@chakra-ui/react';
import { useApi } from '@next/shared/api';
import { AdminUserContext } from '@next/shared/contexts';
import { calculateUnder18 } from '@next/shared/helpers';
import { useMemberData } from 'apps/member-portal/hooks/useMemberData';
import { useImpersonationStore } from 'apps/member-portal/stores/useImpersonationStore';
import { useOnboardingStore } from 'apps/member-portal/stores/useOnboardingStore';
import { formatDate } from 'apps/member-portal/utils';
import { useRouter } from 'next/router';
import React, { useContext, useEffect, useState } from 'react';

const OnboardingCommPref = () => {
  const router = useRouter();
  const { methodApi } = useApi();
  const { setMissingCommPrefIndicator } = useContext(AdminUserContext);
  const {
    firstName,
    lastName,
    alternateId: memberId,
    organizationNo,
    employeeNo,
    dependentNo,
    birthdate,
    isDependent,
    isAdmin,
  } = useMemberData();

  useEffect(() => {
    if (organizationNo && employeeNo) {
      methodApi('memberGetCommunicationPreferences', {
        method: 'POST',
        body: {
          organizationNo,
          employeeNo,
          dependentNo,
        },
        onSuccess: (res) => {
          setEmail(res.data.emailAddress);
        },
        onError: () => {
          console.warn('Failed to fetch communication preferences');
        },
      });
    }
  }, [organizationNo, employeeNo, dependentNo, methodApi]);

  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [phoneTouched, setPhoneTouched] = useState(false);
  const [smsOpted, setSmsOpted] = useState<any>(false);
  const [emailOpted, setemailOpted] = useState<any>(false);
  const [isLoading, setIsLoading] = useState(false);

  const isUnder18 = calculateUnder18(birthdate);
  const { setImpCommunicationComplete } = useImpersonationStore();
  const { needsPrivacyReacceptance, resetOnboardingState } =
    useOnboardingStore.getState();

  const emailFormat =
    /^[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[a-zA-Z0-9](?:[a-z0-9-]*[a-zA-Z0-9])?\.)+[a-zA-Z0-9](?:[a-zA-Z0-9-]*[a-zA-Z0-9])?$/i;

  const handleChangeEmail = (value: string) => {
    const cleanedEmail = value.replace(/\s+/g, '').trim();
    setEmail(cleanedEmail);
  };

  const handleChangePhone = (e: any) => {
    !phoneTouched && setPhoneTouched(true);
    const numericInput = e.target.value.replace(/\D/g, '');
    const formattedPhone = numericInput.replace(
      /(\d{3})(\d{3})(\d{4})/,
      '$1-$2-$3'
    );
    setPhone(formattedPhone);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === ' ' || e.keyCode === 32) {
      e.preventDefault();
    }
  };

  const handleUpdate = () => {
    if (isAdmin) {
      if (needsPrivacyReacceptance) {
        setImpCommunicationComplete(true);
        router.push('/confirm-privacy-policy');
        return;
      } else {
        setImpCommunicationComplete(true);
        router.push('/home');
        return;
      }
    }

    setIsLoading(true);

    const formData = {
      emailAddress: isUnder18 ? '<EMAIL>' : email,
      phoneNumber: isUnder18 ? '0000000000' : phone.replace(/-/g, ''),
      commsPref: {
        smsOpted: isUnder18 ? false : smsOpted,
        emailOpted: isUnder18 ? false : emailOpted,
        callOpted: true,
        voicemailOpted: true,
      },
    };

    methodApi('memberUpdateCommunicationPreferences', {
      method: 'POST',
      body: {
        eventName: 'update',
        source: 'MemberPortal',
        memberId: 'yes',
        firstName,
        lastName,
        organizationNo,
        employeeNo,
        dependentNo,
        updatedBy: isDependent ? dependentNo.toString() : employeeNo.toString(),
        ...formData,
      },
      onSuccess: () => {
        setMissingCommPrefIndicator(false);
        setIsLoading(false);

        if (needsPrivacyReacceptance) {
          router.push('/confirm-privacy-policy');
        } else {
          resetOnboardingState();
          router.push('/home');
        }
      },
      onError: () => {
        setIsLoading(false);
        console.log('error');
      },
    });
  };

  const emailInputError =
    (email === '' && !isUnder18) ||
    (email !== '' && email !== undefined && !email?.match(emailFormat));

  const phoneInputError =
    (phone === '' || phone.replace(/[^0-9]/g, '').length != 10) && phoneTouched;

  return (
    <>
      <Box
        bg="white"
        position="absolute"
        minH="100vh"
        left={0}
        right={0}
        top={0}
        pt={40}
        px={4}
      >
        <Box textAlign="center" mt={6}>
          <Heading>
            <Text fontSize="2xl">Confirm Your Information</Text>
          </Heading>
        </Box>

        {/* Form Box */}
        <Box maxW="480px" mx="auto" py={6} bg="white">
          <Stack gap={0}>
            {/* First Name Field */}
            <HStack
              spacing={4}
              align="center"
              borderBottom="1px solid"
              borderColor="gray.400"
            >
              <Text
                minW="100px"
                m="0"
                p="4"
                w="120px"
                bg="gray.100"
                color="gray.600"
              >
                First Name
              </Text>
              <Text>{firstName}</Text>
            </HStack>

            {/* Last Name Field */}
            <HStack
              spacing={4}
              align="center"
              borderBottom="1px solid"
              borderColor="gray.400"
            >
              <Text
                minW="100px"
                m="0"
                p="4"
                w="120px"
                bg="gray.100"
                color="gray.600"
              >
                Last Name
              </Text>
              <Text>{lastName}</Text>
            </HStack>

            <HStack
              spacing={4}
              align="center"
              borderBottom="1px solid"
              borderColor="gray.400"
            >
              <Text
                minW="100px"
                m="0"
                p="4"
                w="120px"
                bg="gray.100"
                color="gray.600"
              >
                Birthdate
              </Text>
              <Text>
                {birthdate &&
                  formatDate(birthdate?.replace(/-/g, '/').replace(/T.+/, ''))}
              </Text>
            </HStack>
            <HStack
              spacing={4}
              align="center"
              borderBottom="1px solid"
              borderColor="gray.400"
            >
              <Text
                minW="100px"
                m="0"
                p="4"
                w="120px"
                bg="gray.100"
                color="gray.600"
              >
                Member ID
              </Text>
              <Text>{memberId}</Text>
            </HStack>

            {/* Conditionally render the contact info form fields only if user is 18 or older */}
            {!isUnder18 && (
              <>
                <Text
                  textAlign="center"
                  mt={6}
                  color="gray.700"
                  px={2}
                  mb={2}
                  fontSize="16px"
                >
                  Please provide your email and phone number for service
                  communications. Update anytime in account settings.
                  <Text fontStyle={'italic'} pt={2}>
                    Note: These changes will not affect the email you use to log
                    in.
                  </Text>
                </Text>
                <FormControl isRequired isInvalid={emailInputError}>
                  <FormLabel minW="100px" m="0" p="2" fontWeight="bold">
                    Contact Email
                  </FormLabel>
                  <Input
                    placeholder="Email Address"
                    type="email"
                    isRequired
                    p={2}
                    value={email}
                    onChange={(e) => handleChangeEmail(e.target.value)}
                    onKeyDown={handleKeyDown}
                  />
                  <FormErrorMessage>
                    Please enter a valid email address.
                  </FormErrorMessage>
                </FormControl>

                <Box p={2}>
                  <Checkbox
                    size="md"
                    onChange={(e) => setemailOpted(e.target.checked)}
                  >
                    Send me service related notifications via email
                  </Checkbox>
                </Box>

                <FormControl isRequired isInvalid={phoneInputError}>
                  <FormLabel minW="100px" m="0" p="2" fontWeight="bold">
                    Contact Phone
                  </FormLabel>
                  <Input
                    value={phone}
                    onChange={handleChangePhone}
                    type="tel"
                    maxLength={12}
                    placeholder="___-___-____"
                  />
                  <FormErrorMessage>
                    Please enter a valid 10-digit phone number.
                  </FormErrorMessage>
                </FormControl>

                <Box p={2}>
                  <Checkbox
                    size="md"
                    onChange={(e) => setSmsOpted(e.target.checked)}
                    isDisabled={!phone || phoneInputError}
                  >
                    Send me service related notifications via SMS text messages
                  </Checkbox>
                </Box>
              </>
            )}

            <FormControl>
              <Box display="flex" justifyContent="flex-end" mt={4}>
                <Button
                  variant="solid"
                  colorScheme="green"
                  w={['full', 'auto']}
                  isLoading={isLoading}
                  isDisabled={
                    emailInputError ||
                    phoneInputError ||
                    (!isUnder18 && !phoneTouched)
                  }
                  onClick={handleUpdate}
                >
                  Go To My RxBenefits
                </Button>
              </Box>
            </FormControl>
          </Stack>
        </Box>
      </Box>
      <Box bg="white" minH="90vh"></Box>
    </>
  );
};

export default OnboardingCommPref;
