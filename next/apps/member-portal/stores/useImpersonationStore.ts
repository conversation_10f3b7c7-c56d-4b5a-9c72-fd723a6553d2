import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

type ImpersonationMember = {
  alternateId: string;
  birthdate: string;
  clientId: string;
  dependentNo: number | null;
  employeeNo: number;
  expirationDate: string;
  firstName: string;
  generatedId: string;
  lastName: string;
  organizationName: string;
  organizationNo: number;
  personCode: number;
  processStatus: number;
  relationship: string | null;
  zipCode: string;
  dependents?: ImpersonationMember[];
};

type ImpersonationStore = {
  impMember: ImpersonationMember | null;
  setImpMember: (member: ImpersonationMember) => void;
  clearImpersonation: () => void;
  impCommunicationComplete: boolean;
  setImpCommunicationComplete: (complete: boolean) => void;
  impPrivacyAcknowledgedComplete: boolean;
  setImpPrivacyAcknowledgedComplete: (complete: boolean) => void;
};

export const useImpersonationStore = create<ImpersonationStore>()(
  persist(
    (set) => ({
      impMember: null,
      setImpMember: (member) => set({ impMember: member }),
      clearImpersonation: () =>
        set({
          impMember: null,
          impCommunicationComplete: false,
          impPrivacyAcknowledgedComplete: false,
        }),
      impCommunicationComplete: false,
      setImpCommunicationComplete: (complete) =>
        set({ impCommunicationComplete: complete }),
      impPrivacyAcknowledgedComplete: false,
      setImpPrivacyAcknowledgedComplete: (complete) =>
        set({ impPrivacyAcknowledgedComplete: complete }),
    }),
    {
      name: 'impersonation-storage',
      storage: createJSONStorage(() => localStorage),
    }
  )
);
