'use client';
import { useUser } from '@auth0/nextjs-auth0/client';
import {
  Alert,
  Box,
  Button,
  chakra,
  Checkbox,
  Flex,
  FormControl,
  FormLabel,
  Input,
  Radio,
  RadioGroup,
  Select,
  Stack,
  Text,
  Textarea,
} from '@chakra-ui/react';
import { Roles } from '@next/admin/constants';
import { useApi } from '@next/shared/api';
import { useCustomToast } from '@next/shared/hooks';
import { useEffect, useMemo, useState } from 'react';
import { Controller, FormProvider, useForm } from 'react-hook-form';
import { TbAlertTriangleFilled } from 'react-icons/tb';

import { InputError } from '../Global/InputError';
import { Access } from './Access';
type userInfo = {
  firstName: string;
  isActive: boolean;
  lastName: string;
  role: 'RxB Employee' | 'Broker' | 'Client' | null;
  rxbAdmin: boolean;
  username: string;
  psaAccessLevel: number | null;
};
type orgs = {
  brokerFirmId: string;
  brokerOrgId: string;
  brokerParentId: string;
  isActive: boolean;
  name: string;
  organizationNo: number;
}[];
interface useFormInterface {
  role: string;
  brokerPermissions: string;
  edit_members: string | null;
  invoices_no_phi: string | null;
  invoices_phi: string | null;
  invoices_no_pho: string | null;
  noPhiperm: string[];
  phiperm: string[];
  requestorName: string;
  name: string;
  phone: string;
  email: string;
  disclaimer: boolean;
  nda: string;
  rxbPermissions: string;
  generalNotes: string;
}

export const NewUserForm = () => {
  enum theme {
    fontColor = 'rgb(107, 119, 140)',
    buttonColor = '#257389',
  }
  const { user } = useUser();

  const defaultValues = {
    role: '',
    brokerPermissions: '',
    edit_members: null,

    invoices_no_phi: null,
    invoices_phi: null,
    invoices_no_pho: null,
    noPhiperm: [],
    phiperm: [],
    requestorName: `${user?.given_name} ${user?.family_name}`,
    name: '',
    phone: '',
    email: '',
    disclaimer: false,
    nda: '',
    rxbPermissions: '',
    generalNotes: '',
  };
  const method = useForm<useFormInterface>({
    defaultValues: defaultValues,
  });

  const {
    register,
    handleSubmit,
    formState: { errors, isValid, isSubmitted },
    reset,
    watch,
    trigger,
    setValue,
    control,
  } = method;

  const showToast = useCustomToast();
  const { getUserByRole, getApi, methodApi } = useApi();
  const {
    role,
    brokerPermissions,
    edit_members,
    invoices_no_phi,
    invoices_phi,
  } = watch();

  useEffect(() => {
    setValue('brokerPermissions', '');
    setValue('edit_members', null);
    setValue('invoices_no_pho', null);
    setValue('invoices_phi', null);
    setUserOrgs([]);
  }, [role, setValue]);

  const [userOrgs, setUserOrgs] = useState<orgs>(
    getUserByRole?.apManageUserData?.apManageUserOrgs || []
  );

  const [brokerOrg, setBrokerOrg] = useState<any>([]);
  const [brokerParent, setBrokerParent] = useState<any>([]);

  //Temporirly hiding Invoice section
  const hideInvoices = false;

  const [userInfo] = useState<userInfo>({
    firstName: '',
    isActive: true,
    lastName: '',
    role: null,
    rxbAdmin: false,
    username: '',
    psaAccessLevel: null,
  });

  /** The userAccessLevel state below is used to updated the current users psa access level.
   *  The can be dynamically changed by the user and happens in the Access component, meaning
   *  we need to pass the setUserAccessLevel function to the Access component so that this
   *  state gets updated properly for submission. See the onSubmit process below to see how
   *  userAccessLevel state should be handled within this component.
   */

  const [, setUserAccessLevel] = useState<number | null>(
    userInfo.psaAccessLevel
  );

  const selectedRole = watch('role');

  useEffect(() => {
    if (!role) return;
    let tempRole = role;
    if (tempRole === 'Vendor') tempRole = 'Client';
    getApi(
      'getUserByRole',
      { role: Roles[tempRole as keyof typeof Roles] },
      {
        onSuccess: () => {
          //   setTempPerms(undefined);
        },
      }
    );
  }, [userInfo.role, getApi, setValue, role]);

  const onSubmit = (data: any) => {
    /** Be sure to include the psaAccessLevel: userAccessLevel as shown below
     *  so that we are updating the psaAccessLevel of the user if needed.
     */

    const clientOrg = userOrgs?.reduce<string[]>(
      (accumulator, currentValue) => {
        return [...accumulator, currentValue.name];
      },
      []
    );

    const brokerOrganization = brokerOrg?.reduce(
      (accumulator: any, currentValue: { accountName: any }) => {
        return [...accumulator, currentValue.accountName];
      },
      []
    );

    if (brokerPermissions) {
      const permissions = RoleClient[brokerPermissions] || [];

      if (
        brokerPermissions === 'broker_view_members' ||
        brokerPermissions === 'client_view_members' ||
        brokerPermissions === 'vendor_view_members'
      ) {
        permissions.forEach((permission: string) => {
          data?.phiperm.push(permission);
        });
        if (edit_members) {
          data?.phiperm.push('Edit Members');
        }
      } else {
        permissions.forEach((permission: string) => {
          data?.noPhiperm.push(permission);
        });
      }
    }

    const apiBody = {
      submitterName: data?.requestorName,
      firstName: data?.name?.split(' ')[0],
      lastName: data?.name?.split(' ')[1],
      phoneNumber: data?.phone,
      corporateEmail: data?.email,
      role: data?.role,
      disclaimer: data?.disclaimer,
      brokerFirmParent:
        selectedRole === 'Broker' ? brokerParent?.accountName : '',
      brokerFirms: selectedRole === 'Broker' ? brokerOrganization : [],
      clientOrganizations: clientOrg,
      noPhiPermissions: data?.noPhiperm,
      phiPermissions: data?.phiperm,
      needsCaNda: data?.nda === '1' ? true : false,
    };

    methodApi('newUserRequestForm', {
      method: 'POST',
      body: apiBody,
    }).then(() => {
      showToast({
        title: `Form submitted successfully`,
        status: 'success',
      });

      reset();
    });
  };

  const optionalPermSelection = useMemo(() => {
    const optionalBools: any = {
      Broker: [edit_members, invoices_no_phi, invoices_phi],
      Client: [edit_members],
      Vendor: [edit_members, invoices_no_phi, invoices_phi],
    };
    const optionalVals: any = {
      Broker: {
        edit_members: 'Edit Members',
        invoices_no_phi: ['View Invoice', 'View Invoice Claim Summary'],
        invoices_phi: [
          'View Invoice',
          'View Invoice Claim Detail',
          'View Invoice Claim Export',
        ],
      },
      Client: {
        edit_members: 'Edit Members',
      },
      Vendor: {
        edit_members: 'Edit Members',
        invoices_no_phi: ['View Invoice', 'View Invoice Claim Summary'],
        invoices_phi: [
          'View Invoice',
          'View Invoice Claim Detail',
          'View Invoice Claim Export',
        ],
      },
    };
    if (role && !optionalVals?.[role]) return [];
    else {
      const perms = new Set(
        Object.keys(optionalVals?.[role] || {})
          ?.filter((perm: any, index: number) => optionalBools?.[role]?.[index])
          ?.map((perm: any) => {
            return optionalVals?.[role]?.[perm];
          })
          .flatMap((e) => e)
      );
      return [...perms];
    }
  }, [edit_members, invoices_no_phi, invoices_phi, role]);

  const RoleClient: any = useMemo(() => {
    const permBase: any = {
      default_broker: ["View Organization's, Plans & Groups", 'View Files'],
      default_client: ["View Organization's, Plans & Groups", 'View Files'],
      default_vendor: ["View Organization's, Plans & Groups", 'View Files'],
    };
    permBase.broker_pharmacy_consultant = permBase?.default_broker;
    permBase.broker_view_members = [...permBase.default_broker, 'View Members'];
    permBase.vendor_view_members = [...permBase.default_vendor, 'View Members'];
    permBase.client_view_members = [...permBase.default_client, 'View Members'];
    return permBase;
  }, []);

  const basePermsList = useMemo(() => {
    if (!getUserByRole?.apManageUserPerms) return [];
    if (!RoleClient?.[brokerPermissions]) return [];
    const singleRoleClient = [...RoleClient[brokerPermissions]];
    singleRoleClient.push(...optionalPermSelection);

    return getUserByRole?.apManageUserPerms?.filter((perm: any) =>
      singleRoleClient.includes(perm?.permissionName)
    );
  }, [
    RoleClient,
    brokerPermissions,
    getUserByRole?.apManageUserPerms,
    optionalPermSelection,
  ]);

  const hasCaNda = useMemo(() => {
    const PHI = [
      'View Members',
      'Edit Members',
      'View Invoice Claim Detail',
      'View Invoice Claim Export',
    ];
    if (brokerPermissions === 'Client') return false;
    const hasPHI = basePermsList.filter((key: any) =>
      PHI.includes(key?.permissionName)
    );
    return !!hasPHI.length;
  }, [basePermsList, brokerPermissions]);

  const phiReports = [
    'Full Claims Details',
    'Stop Loss Details',
    'Stop Loss Summary',
  ];
  const reports = [
    'Claims Experience',
    'Lag Report',
    'Spend & Utilization Package',
  ];
  const isAnyPhiReportTouched = phiReports.some(
    (report) => watch('phiperm') && watch('phiperm')?.includes(String(report))
  );

  return (
    <Box
      p={8}
      borderRadius="md"
      boxShadow="md"
      w={{ base: '100%', md: '1024px' }}
      padding={'40px 100px 0'}
      mx="auto"
      mt={8}
      pb={5}
      bg={'white'}
    >
      <FormProvider {...method}>
        {' '}
        <form onSubmit={handleSubmit(onSubmit)}>
          <Stack spacing={6}>
            <Text fontSize="2xl" fontWeight="bold">
              User Account Request
            </Text>

            <Stack direction={{ base: 'column', md: 'row' }} spacing={6}>
              <FormControl id="requestor-name">
                <FormLabel color={theme.fontColor}>
                  RxB Requestor <chakra.span color={'red'}>*</chakra.span>
                </FormLabel>
                <Text style={{ fontSize: '11px', color: theme.fontColor }}>
                  Submitter&apos;s Name
                </Text>
                <Input
                  readOnly
                  type="text"
                  value={`${user?.given_name} ${user?.family_name}`}
                  {...register('requestorName', {
                    required: true,
                  })}
                  isInvalid={errors.requestorName as any}
                />
                {errors.requestorName && (
                  <InputError message={'This field is required.'} />
                )}
              </FormControl>
            </Stack>

            <Text fontSize="12px" fontWeight="bold" color={theme.fontColor}>
              Who needs an account?
            </Text>

            <Stack direction={{ base: 'column', md: 'row' }} spacing={6}>
              <FormControl id="name">
                <FormLabel color={theme.fontColor} fontWeight={'bold'}>
                  Name
                </FormLabel>
                <Text style={{ fontSize: '11px', color: theme.fontColor }}>
                  First and Last
                </Text>

                <Input type="text" {...register('name')} />
              </FormControl>

              <FormControl id="phone">
                <FormLabel color={theme.fontColor} fontWeight={'bold'}>
                  Phone Number
                </FormLabel>
                <Text style={{ fontSize: '11px', color: theme.fontColor }}>
                  Optional
                </Text>
                <Input type="text" {...register('phone')} />
              </FormControl>
            </Stack>

            <Stack direction={{ base: 'column', md: 'row' }} spacing={6}>
              <FormControl id="email">
                <FormLabel color={theme.fontColor} fontWeight={'bold'}>
                  Corporate Email <chakra.span color={'red'}>*</chakra.span>
                </FormLabel>
                <Input
                  type="email"
                  {...register('email', {
                    required: true,
                  })}
                  isInvalid={errors.email as any}
                />
                {errors?.email && (
                  <InputError message={'This field is required.'} />
                )}
                <Text style={{ fontSize: '11px', color: theme.fontColor }}>
                  Note: user will need access to this email to complete their
                  log-in
                </Text>
              </FormControl>

              <FormControl>
                <FormLabel color={theme.fontColor} fontWeight={'bold'}>
                  Role Select
                </FormLabel>
                <Controller
                  name="role"
                  control={control}
                  render={({ field }) => (
                    <RadioGroup value={field.value} onChange={field.onChange}>
                      <Stack direction="column">
                        <Radio value="Broker">Broker</Radio>
                        <Radio value="Client">Client</Radio>
                        <Radio value="Vendor">Vendor</Radio>
                      </Stack>
                    </RadioGroup>
                  )}
                />
              </FormControl>
            </Stack>

            {/* Role-specific sections */}
            <Box>
              {/* Broker Section */}
              {watch('role') === 'Broker' && (
                <Box
                  display={watch('role') === 'Broker' ? 'block' : 'none'}
                  gap={2}
                >
                  <Text fontSize="l" fontWeight="bold" color={theme.fontColor}>
                    Broker Access & Permissions
                  </Text>
                  <FormControl id="broker-parent" paddingBottom={'30px'}>
                    <Text fontSize={'11px'} color={theme.fontColor}>
                      Limited to one, can also be &apos;No Parent&apos;
                    </Text>

                    <Access
                      setUserOrgs={setUserOrgs}
                      setBrokerOrg={setBrokerOrg}
                      setBrokerParent={setBrokerParent}
                      // eslint-disable-next-line jsx-a11y/aria-role
                      role={'Broker'}
                      orgs={getUserByRole?.apManageUserData?.apManageUserOrgs}
                      unselectOrgs={
                        getUserByRole?.apManageUserData?.apManageUserOrgs
                      }
                      brokerData={{
                        parents: getUserByRole?.brokerParents,
                        firms: getUserByRole?.brokerFirms,
                      }}
                      hideStyles
                      setUserAccessLevel={setUserAccessLevel}
                      disableSelectAll={true}
                    />
                  </FormControl>
                  <FormControl id="broker-permissions">
                    <FormLabel>
                      Broker Permissions Template
                      <chakra.span color={'red'}>*</chakra.span>{' '}
                    </FormLabel>
                    <Select
                      {...register('brokerPermissions', {
                        required: selectedRole === 'Broker',
                        onChange(e) {
                          setValue('edit_members', null);
                        },
                      })}
                      isInvalid={errors.brokerPermissions as any}
                      placeholder="Select..."
                    >
                      <option value="default_broker">
                        Default Broker (no PHI)
                      </option>
                      <option value="broker_view_members">
                        Broker View Members (PHI)
                      </option>
                      <option value="broker_pharmacy_consultant">
                        Broker Pharmacy Consultant/Reporting (no PHI)
                      </option>
                    </Select>
                    {errors?.brokerPermissions && (
                      <InputError message={'This field is required.'} />
                    )}
                  </FormControl>
                  <Stack spacing={4}>
                    {watch('brokerPermissions') === 'broker_view_members' && (
                      <FormControl>
                        <Text color={theme.fontColor} fontWeight={'bold'}>
                          Broker Optional Permissions
                          <br />
                          <chakra.span
                            style={{
                              fontSize: '11px',
                              color: theme.fontColor,
                              fontWeight: 'normal',
                            }}
                          >
                            Select only if required for user.
                          </chakra.span>
                        </Text>
                        <Checkbox
                          {...register('edit_members')}
                          size={'sm'}
                          fontWeight={'bold'}
                          color={theme.fontColor}
                        >
                          Edit Members
                        </Checkbox>
                      </FormControl>
                    )}

                    <FormControl>
                      {hideInvoices && (
                        <>
                          <Text color={theme.fontColor} fontWeight={'bold'}>
                            Restrictions
                            {watch('role') === 'Broker' && (
                              <chakra.span color={'red'}> * </chakra.span>
                            )}
                            <br />
                            {watch('role') !== 'Broker' && (
                              <chakra.span
                                fontSize={'11px'}
                                fontWeight={'normal'}
                              >
                                Optional
                              </chakra.span>
                            )}
                          </Text>
                          <Flex flexDir={'column'}>
                            <Checkbox
                              {...register('invoices_no_phi')}
                              size={'sm'}
                              color={theme.fontColor}
                            >
                              Invoices (no PHI)
                            </Checkbox>
                            <Checkbox
                              {...register('invoices_phi')}
                              size={'sm'}
                              color={theme.fontColor}
                            >
                              Invoices (PHI)
                            </Checkbox>
                          </Flex>
                        </>
                      )}
                      <Flex flexDir={'column'} paddingTop={'30px'}>
                        <Text color={theme.fontColor} fontWeight={'bold'}>
                          Reports
                        </Text>
                        {reports.map((key, index) => (
                          <Checkbox
                            key={key + index}
                            value={key}
                            {...register('noPhiperm')}
                            size={'sm'}
                            color={theme.fontColor}
                          >
                            {key}
                          </Checkbox>
                        ))}
                        <Text
                          color={theme.fontColor}
                          fontWeight={'bold'}
                          paddingTop={'30px'}
                        >
                          PHI Reports
                        </Text>
                        {phiReports.map((key, index) => (
                          <Checkbox
                            key={key + index}
                            value={key}
                            {...register('phiperm')}
                            size={'sm'}
                            color={theme.fontColor}
                          >
                            {key}
                          </Checkbox>
                        ))}
                      </Flex>
                    </FormControl>

                    <FormControl>
                      <Text color={theme.fontColor} fontWeight={'bold'}>
                        <span>
                          {hasCaNda ||
                          isAnyPhiReportTouched ||
                          watch('edit_members') ||
                          watch('invoices_phi')
                            ? 'CA/NDA'
                            : 'NDA'}
                        </span>
                        <chakra.span color={'red'}> * </chakra.span>
                      </Text>
                      <Text
                        fontSize={'11px'}
                        fontWeight={'normal'}
                        color={theme.fontColor}
                      >
                        Is there{' '}
                        {hasCaNda ||
                        isAnyPhiReportTouched ||
                        watch('edit_members') ||
                        watch('invoices_phi')
                          ? 'a CA + NDA (or CA/NDA)'
                          : 'an NDA'}{' '}
                        on file with Brokerage Firm entity?
                      </Text>
                      <Select
                        placeholder="Select..."
                        {...register('nda', {
                          required: selectedRole === 'Broker',
                        })}
                        isInvalid={errors.nda as any}
                      >
                        <option value="1">Yes</option>
                        <option value="0">No</option>
                      </Select>
                      {errors?.nda && (
                        <InputError message={'This field is required.'} />
                      )}
                    </FormControl>

                    <FormControl>
                      <Text size={'md'}>Disclaimer</Text>
                      <Checkbox
                        {...register('disclaimer', {
                          required: selectedRole === 'Broker',
                          onChange(event) {
                            trigger('disclaimer');
                          },
                        })}
                        size={'sm'}
                        color={theme.fontColor}
                        border={
                          errors.disclaimer ? '1px dashed red' : 'inherit'
                        }
                      >
                        I understand that making this request will result in
                        someone outside of RxBenefits being granted the ability
                        to see important data.
                      </Checkbox>
                    </FormControl>
                  </Stack>
                </Box>
              )}

              {/* ClientSection */}
              {watch('role') === 'Client' && (
                <Box>
                  <Text fontSize="xl" fontWeight="normal">
                    Client Access & Permissions
                  </Text>

                  <Access
                    setUserOrgs={setUserOrgs}
                    setBrokerOrg={setBrokerOrg}
                    setBrokerParent={setBrokerParent}
                    // eslint-disable-next-line jsx-a11y/aria-role
                    role={'Client'}
                    orgs={getUserByRole?.apManageUserData?.apManageUserOrgs}
                    unselectOrgs={
                      getUserByRole?.apManageUserData?.apManageUserOrgs
                    }
                    brokerData={{
                      parents: getUserByRole?.brokerParents,
                      firms: getUserByRole?.brokerFirms,
                    }}
                    hideStyles
                    setUserAccessLevel={setUserAccessLevel}
                  />
                  <FormLabel>
                    Client Permissions Template
                    <chakra.span color={'red'}>*</chakra.span>
                  </FormLabel>
                  <Select
                    {...register('brokerPermissions', {
                      required: selectedRole === 'Client',
                      onChange(e) {
                        setValue('edit_members', null);
                      },
                    })}
                    isInvalid={errors.brokerPermissions as any}
                    placeholder="Select..."
                  >
                    <option value="default_client">
                      Default Client (no PHI)
                    </option>
                    <option value="client_view_members">
                      Client View Members (PHI)
                    </option>
                  </Select>
                  {watch('brokerPermissions') === 'client_view_members' && (
                    <FormControl>
                      <Text
                        color={theme.fontColor}
                        fontWeight={'bold'}
                        paddingTop={'30px'}
                      >
                        Client Optional Permissions
                        <br />
                        <chakra.span
                          style={{
                            fontSize: '11px',
                            color: theme.fontColor,
                            fontWeight: 'normal',
                          }}
                        >
                          Select only if required for user.
                        </chakra.span>
                      </Text>
                      <Flex flexDirection={'column'}>
                        <Checkbox
                          paddingTop={'10px'}
                          paddingLeft={'10px'}
                          {...register('edit_members')}
                          size={'sm'}
                          color={theme.fontColor}
                        >
                          Edit Members
                        </Checkbox>
                      </Flex>
                    </FormControl>
                  )}
                  {hideInvoices && (
                    <FormControl paddingTop={'30px'}>
                      <Text color={theme.fontColor} fontWeight={'bold'}>
                        Restrictions
                        <br />
                        <chakra.span fontSize={'11px'} fontWeight={'normal'}>
                          {' '}
                          Optional
                        </chakra.span>
                      </Text>
                      <Flex flexDir={'column'}>
                        <Checkbox
                          {...register('invoices_no_phi')}
                          size={'sm'}
                          color={theme.fontColor}
                        >
                          Invoices (no PHI)
                        </Checkbox>
                        <Checkbox
                          {...register('invoices_phi')}
                          size={'sm'}
                          color={theme.fontColor}
                        >
                          Invoices (PHI)
                        </Checkbox>
                      </Flex>
                    </FormControl>
                  )}

                  <Flex flexDir={'column'} paddingTop={'30px'}>
                    <Text color={theme.fontColor} fontWeight={'bold'}>
                      Reports
                    </Text>
                    {reports.map((key, index) => (
                      <Checkbox
                        key={key + index}
                        value={key}
                        {...register('noPhiperm')}
                        size={'sm'}
                        color={theme.fontColor}
                      >
                        {key}
                      </Checkbox>
                    ))}
                    <Text
                      color={theme.fontColor}
                      fontWeight={'bold'}
                      paddingTop={'30px'}
                    >
                      PHI Reports
                    </Text>
                    {phiReports.map((key, index) => (
                      <Checkbox
                        key={key + index}
                        value={key}
                        {...register('phiperm')}
                        size={'sm'}
                        color={theme.fontColor}
                      >
                        {key}
                      </Checkbox>
                    ))}
                  </Flex>

                  <FormControl mt={2}>
                    <Text color={theme.fontColor} fontWeight={'bold'}>
                      <span>BAA</span>
                      <chakra.span color={'red'}> * </chakra.span>
                    </Text>
                    <Text
                      fontSize={'11px'}
                      fontWeight={'normal'}
                      color={theme.fontColor}
                    >
                      Is there{' '}
                      {hasCaNda ||
                      isAnyPhiReportTouched ||
                      watch('invoices_phi')
                        ? 'a CA + NDA (or CA/NDA)'
                        : 'an NDA'}{' '}
                      on file with Vendor entity?
                    </Text>
                    <Select
                      placeholder="Select..."
                      {...register('nda', {
                        required: selectedRole === 'Broker',
                      })}
                      isInvalid={errors.nda as any}
                    >
                      <option value="1">Yes</option>
                      <option value="0">No</option>
                    </Select>
                    {errors?.nda && (
                      <InputError message={'This field is required.'} />
                    )}
                  </FormControl>
                </Box>
              )}

              {/* Vendor Section */}
              {watch('role') === 'Vendor' && (
                <Box gap={2}>
                  <Text fontSize="l" fontWeight="bold" color={theme.fontColor}>
                    Vendor Access & Permissions
                  </Text>
                  <FormControl id="broker-parent" paddingBottom={'30px'}>
                    <Text fontSize={'11px'} color={theme.fontColor}>
                      Limited to one, can also be &apos;No Parent&apos;
                    </Text>

                    <Access
                      setUserOrgs={setUserOrgs}
                      setBrokerOrg={setBrokerOrg}
                      setBrokerParent={setBrokerParent}
                      // eslint-disable-next-line jsx-a11y/aria-role
                      role={'Client'}
                      orgs={getUserByRole?.apManageUserData?.apManageUserOrgs}
                      unselectOrgs={
                        getUserByRole?.apManageUserData?.apManageUserOrgs
                      }
                      brokerData={{
                        parents: getUserByRole?.brokerParents,
                        firms: getUserByRole?.brokerFirms,
                      }}
                      hideStyles
                      setUserAccessLevel={setUserAccessLevel}
                    />
                  </FormControl>

                  <FormControl id="broker-permissions">
                    <FormLabel>
                      Vendor Permissions Template
                      <chakra.span color={'red'}>*</chakra.span>
                    </FormLabel>
                    <Select
                      {...register('brokerPermissions', {
                        required: selectedRole === 'Vendor',
                        onChange(e) {
                          setValue('edit_members', null);
                        },
                      })}
                      isInvalid={errors.brokerPermissions as any}
                      placeholder="Select..."
                    >
                      <option value="default_vendor">
                        Default Vendor (no PHI)
                      </option>
                      <option value="vendor_view_members">
                        Vendor View Members (PHI)
                      </option>
                    </Select>
                    {errors?.brokerPermissions && (
                      <InputError message={'This field is required.'} />
                    )}
                  </FormControl>
                  <Stack spacing={4}>
                    {watch('brokerPermissions') === 'vendor_view_members' && (
                      <FormControl>
                        <Text color={theme.fontColor} fontWeight={'bold'}>
                          Vendor Optional Permissions
                          <br />
                          <chakra.span
                            style={{
                              fontSize: '11px',
                              color: theme.fontColor,
                              fontWeight: 'normal',
                            }}
                          >
                            Select only if required for user.
                          </chakra.span>
                        </Text>
                        <Checkbox
                          {...register('edit_members')}
                          size={'sm'}
                          fontWeight={'bold'}
                          color={theme.fontColor}
                        >
                          Edit Members
                        </Checkbox>
                      </FormControl>
                    )}
                    {hideInvoices && (
                      <FormControl>
                        <Text color={theme.fontColor} fontWeight={'bold'}>
                          Restrictions
                          <br />
                          <chakra.span fontSize={'11px'} fontWeight={'normal'}>
                            {' '}
                            Optional
                          </chakra.span>
                        </Text>
                        <Flex flexDir={'column'}>
                          <Checkbox
                            {...register('invoices_no_phi')}
                            size={'sm'}
                            color={theme.fontColor}
                          >
                            Invoices (no PHI)
                          </Checkbox>
                          <Checkbox
                            {...register('invoices_phi')}
                            size={'sm'}
                            color={theme.fontColor}
                          >
                            Invoices (PHI)
                          </Checkbox>
                        </Flex>
                      </FormControl>
                    )}

                    <Flex flexDir={'column'} paddingTop={'30px'}>
                      <Text color={theme.fontColor} fontWeight={'bold'}>
                        Reports
                      </Text>
                      {reports.map((key, index) => (
                        <Checkbox
                          key={key + index}
                          value={key}
                          {...register('noPhiperm')}
                          size={'sm'}
                          color={theme.fontColor}
                        >
                          {key}
                        </Checkbox>
                      ))}
                      <Text
                        color={theme.fontColor}
                        fontWeight={'bold'}
                        paddingTop={'30px'}
                      >
                        PHI Reports
                      </Text>
                      {phiReports.map((key, index) => (
                        <Checkbox
                          key={key + index}
                          value={key}
                          {...register('phiperm')}
                          size={'sm'}
                          color={theme.fontColor}
                        >
                          {key}
                        </Checkbox>
                      ))}
                    </Flex>

                    <FormControl>
                      <Text color={theme.fontColor} fontWeight={'bold'}>
                        <span>
                          {hasCaNda ||
                          isAnyPhiReportTouched ||
                          watch('invoices_phi') ||
                          watch('edit_members')
                            ? 'CA/NDA'
                            : 'NDA'}
                        </span>
                        <chakra.span color={'red'}> * </chakra.span>
                      </Text>
                      <Text
                        fontSize={'11px'}
                        fontWeight={'normal'}
                        color={theme.fontColor}
                      >
                        Is there{' '}
                        {hasCaNda ||
                        isAnyPhiReportTouched ||
                        watch('invoices_phi') ||
                        watch('edit_members')
                          ? 'a CA + NDA (or CA/NDA)'
                          : 'an NDA'}{' '}
                        on file with Vendor entity?
                      </Text>
                      <Select
                        placeholder="Select..."
                        {...register('nda', {
                          required: selectedRole === 'Broker',
                        })}
                        isInvalid={errors.nda as any}
                      >
                        <option value="1">Yes</option>
                        <option value="0">No</option>
                      </Select>
                      {errors?.nda && (
                        <InputError message={'This field is required.'} />
                      )}
                    </FormControl>

                    <FormControl>
                      <Text size={'md'}>Disclaimer</Text>
                      <Checkbox
                        {...register('disclaimer', {
                          required: selectedRole === 'Broker',
                          onChange(event) {
                            trigger('disclaimer');
                          },
                        })}
                        size={'sm'}
                        color={theme.fontColor}
                        border={
                          errors.disclaimer ? '1px dashed red' : 'inherit'
                        }
                      >
                        I understand that making this request will result in
                        someone outside of RxBenefits being granted the ability
                        to see important data.
                      </Checkbox>
                    </FormControl>
                  </Stack>
                </Box>
              )}

              {/* RxB Associate Section */}
              <Box
                display={watch('role') === 'rxb-associate' ? 'block' : 'none'}
              >
                <Text fontSize="xl" fontWeight="normal">
                  RxB Associate Access & Permissions
                </Text>

                <FormControl id="rxb-permissions">
                  <FormLabel>
                    RxB Associate Permissions Template{' '}
                    <chakra.span color={'red'}>*</chakra.span>
                  </FormLabel>
                  <Select
                    {...register('rxbPermissions', {
                      required: selectedRole === 'rxb-associate',
                      onChange(e) {
                        setValue('edit_members', null);
                      },
                    })}
                    isInvalid={errors.rxbPermissions as any}
                    placeholder="Select..."
                  >
                    <option value="broker-relations">Broker Relations</option>
                    <option value="account_management">
                      Account Management/Solution Advisors
                    </option>
                    <option value="protect_cds">Protect CDS</option>
                    <option value="data_management">Data Management</option>
                    <option value="financial_management_billing">
                      Financial Management: Billing
                    </option>
                    <option value="financial_management_commissions">
                      Financial Management: Commissions
                    </option>
                    <option value="it_helpdesk_prod_support">
                      IT Helpdesk & Prod Support
                    </option>
                  </Select>
                </FormControl>

                <FormControl id="general-notes">
                  <FormLabel>General Notes Description</FormLabel>
                  <Textarea {...register('generalNotes')} />
                </FormControl>
              </Box>
            </Box>

            {!isValid && isSubmitted && (
              <Alert mt={2} variant={'solid'} status="error">
                <TbAlertTriangleFilled
                  style={{ color: 'white', fontSize: '30px', margin: '10px' }}
                />

                <p style={{ fontSize: '16px', color: 'white' }}>
                  Submission failed. Please change any invalid answers and
                  ensure all required fields are completed.
                </p>
              </Alert>
            )}

            <Stack direction={{ base: 'column', md: 'row' }} spacing={6}>
              <Button
                type="submit"
                bg="teal.500"
                color="white"
                _hover={{ bg: 'teal.600' }}
              >
                Create
              </Button>
              <Button
                type="button"
                variant="link"
                color="blue.600"
                onClick={() => {
                  reset(defaultValues);
                }}
              >
                Cancel
              </Button>
            </Stack>
          </Stack>
        </form>
      </FormProvider>
    </Box>
  );
};
