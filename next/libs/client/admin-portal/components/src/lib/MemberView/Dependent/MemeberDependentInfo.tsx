import { Accordion, Flex } from '@chakra-ui/react';
import {
  DependentForm,
  ReinstatorCard,
  TerminatorCard,
} from '@next/admin/components';
import {
  GET_DEPENDENT_ADDRESS,
  GET_DEPENDENT_BASIC_INFO_FORM_ITEMS,
  GET_DEPENDENT_CLIENT_DEFINED_DATA,
  GET_DEPENDENT_GROUP_ASSIGNMENT,
  GET_DEPENDENT_IDENTITY,
  theme,
} from '@next/admin/constants';
import { calculateAge, formatSSN } from '@next/shared/helpers';
import { useCustomToast } from '@next/shared/hooks';
import { useParams } from 'next/navigation';
import React from 'react';
import { useForm } from 'react-hook-form';

interface MemeberDependentInfoProps {
  SupportsDifferentDependentAddress: boolean;
  dependentData: Record<string, any>;
  employeeData: Record<string, any>;
  handleReinstation: (payload: { reinstateDate: string }) => void;
  handleTermination: (payload: { terminationDate: string }) => void;
  handleRemoveTermination: () => void;
  apiRequestInstance: Record<string, any>;
}

export const MemeberDependentInfo: React.FC<MemeberDependentInfoProps> = ({
  SupportsDifferentDependentAddress,
  dependentData,
  employeeData,
  handleReinstation,
  handleTermination,
  handleRemoveTermination,
  apiRequestInstance,
}) => {
  const formInstance = useForm();
  const { setValue } = formInstance;
  const disabledFormInstance = useForm();
  const { getApi, methodApi, resetData, getDependentSSN } = apiRequestInstance;
  const showToast = useCustomToast();
  const params = useParams();
  const orgId = params?.['organizationId'];
  const empId = params?.['employeeId'];
  const dependentId = params?.['dependentId'];

  const getSSNId = () => {
    getApi('getDependentSSN', {
      orgId,
      empId,
      dependentId,
    });
  };

  if (dependentData) {
    setValue(
      'medDSubsidyEffectiveDate',
      dependentData?.medDSubsidyEffectiveDate
    );
    GET_DEPENDENT_BASIC_INFO_FORM_ITEMS.forEach((field: any) => {
      const { fieldName } = field;

      if (fieldName === 'age') {
        // Calculate age based on birthdate
        const birthdate = dependentData['birthdate'];
        if (birthdate) {
          const age = calculateAge(birthdate);
          field.defaultValue = age;
        }
      } else if (fieldName in dependentData) {
        field.defaultValue = dependentData[fieldName];
      }
    });

    GET_DEPENDENT_ADDRESS.forEach((field) => {
      const { fieldName } = field;
      if (fieldName in dependentData) {
        field.defaultValue = dependentData[fieldName]
          ? dependentData[fieldName]
          : '';
      }
    });

    GET_DEPENDENT_IDENTITY.forEach((field) => {
      const { fieldName } = field;
      if (fieldName in dependentData) {
        field.defaultValue = dependentData[fieldName];
      }
      if (getDependentSSN?.ssn) {
        formInstance.setValue('ssn', formatSSN(getDependentSSN.ssn));
      }
    });

    GET_DEPENDENT_CLIENT_DEFINED_DATA.forEach((field) => {
      const { fieldName } = field;
      if (fieldName in dependentData) {
        field.defaultValue = dependentData[fieldName] || '';
      }
    });
  }

  if (employeeData) {
    GET_DEPENDENT_GROUP_ASSIGNMENT.forEach((field) => {
      const { fieldName } = field;
      const dependentGroupValue = dependentData?.['alternateDependentGroup'];

      const employeeViewGroup = dependentGroupValue
        ? apiRequestInstance?.employeeGroupOptions?.filter(
            (each: any) =>
              String(each.dependentAlternateGroupNumber) ===
              String(dependentGroupValue)
          )
        : [];

      if (fieldName in employeeData && fieldName === 'groupName') {
        field.defaultValue = employeeViewGroup?.[0]
          ? `${employeeViewGroup[0].benefitGroupId}-${employeeViewGroup[0].name}`
          : '';
      } else if (fieldName in employeeData) {
        field.defaultValue = employeeData[fieldName];
      }
    });
  }

  if (getDependentSSN && 'ssn' in getDependentSSN) {
    GET_DEPENDENT_IDENTITY.forEach((field) => {
      formInstance.setValue('ssn', formatSSN(getDependentSSN?.ssn || ''));
    });
  }

  const onSubmit = (values: any) => {
    let newCOBSecondary = values?.isCOBSecondary;

    if (!values?.cobSecondaryDate && !values.cobPrimaryDate) {
      newCOBSecondary = null;
    }

    const updatedValues = {
      ...values,
      isCOBSecondary: newCOBSecondary,
      personCode: values?.personCode ? Number(values?.personCode) : null,
    };

    if (!updatedValues?.cobSecondaryDate) {
      updatedValues.cobSecondaryDate = null;
    }

    if (!updatedValues.cobPrimaryDate) {
      updatedValues.cobPrimaryDate = null;
    }

    methodApi('updateDependentView', {
      method: 'PUT',
      restParams: {
        orgId,
        empId,
        dependentId,
      },
      body: updatedValues,
      onSuccess: () => {
        formInstance.reset();
        resetData('employeeView');
        resetData('dependentView');
        showToast({
          title: 'Data Updated Successfully',
          status: 'success',
        });
        getApi('employeeView', { orgId, empId });
        getApi('dependentView', { orgId, empId, dependentId });
      },
      onError: () =>
        showToast({
          title: 'Failed to Update Data',
          status: 'error',
        }),
    });
  };

  return (
    <Flex background={theme.colors.brand.lightestgray} padding={8} gap={8}>
      <Flex flex={8}>
        <Accordion allowMultiple width={'100%'} defaultIndex={[0]}>
          <DependentForm
            SupportsDifferentDependentAddress={
              SupportsDifferentDependentAddress
            }
            employeeData={employeeData}
            dependentData={dependentData}
            onSubmit={onSubmit}
            getSSNId={getSSNId}
            formInstance={formInstance}
            apiInstance={apiRequestInstance}
            disabledFormInstance={disabledFormInstance}
          />
        </Accordion>
      </Flex>

      <Flex flex={4} flexDirection={'column'} gap={4}>
        {dependentData?.processStatus === 9 &&
        (dependentData?.benefitGroupExpirationDate === null ||
          dependentData?.organizationExpirationDate === null ||
          dependentData?.dependentExpirationDate === null) ? (
          <ReinstatorCard
            reinstateTitle={'Reinstate Dependent'}
            onReinstation={handleReinstation}
            onRemoveTermination={handleRemoveTermination}
            minDate={dependentData?.effectiveDate}
            empExpiryDate={dependentData?.employeeExpirationDate}
          />
        ) : (
          <TerminatorCard
            onTermination={handleTermination}
            cardTitle={'Terminate Dependent'}
            minDate={dependentData?.effectiveDate}
          />
        )}
      </Flex>
    </Flex>
  );
};
