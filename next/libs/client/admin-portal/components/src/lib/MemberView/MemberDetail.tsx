'use client';
import {
  AccordionPanel,
  Button,
  Checkbox,
  Divider,
  Flex,
  FormControl,
  FormHelperText,
  FormLabel,
  Input,
  Select,
  Textarea,
  Tooltip,
} from '@chakra-ui/react';
import { createStandaloneToast } from '@chakra-ui/react';
import { PermissionRoleContext } from '@next/shared/contexts';
import {
  calculateAge,
  convertToISODate,
  formatDate,
} from '@next/shared/helpers';
import { AccordionWrapper } from '@next/shared/ui';
import { createColumnHelper } from '@tanstack/react-table';
import { STATE_CODE_MAPPING } from 'libs/client/admin-portal/constants/src/lib/state';
import { theme } from 'libs/client/admin-portal/constants/src/lib/theme';
import { useParams } from 'next/navigation';
import React, { useContext, useEffect, useMemo, useState } from 'react';
import { Controller, FieldError, FormProvider, useForm } from 'react-hook-form';
import { AiOutlineQuestionCircle } from 'react-icons/ai';
import ReactSelect from 'react-select';

import { CustomReactTable } from '../Global/CustomReactTable';
import { COB } from './COB';
import { IncentivePrograms } from './IncentivePrograms';
import { MedDSubsidy } from './MedDSubsidy';

export type MemberDetailDataProps<Data extends Record<string, any>> = {
  supportClientDefinedData: boolean;
  data: Data;
  ssn: number;
  apiInstance: Record<string, any>;
};
interface StateManagedSelect {
  label: string;
  value: string;
}
interface MembersDetailData {
  groupEffectiveDate?: string | number | undefined;
  firstName: string;
  middleInitial: string | null;
  lastName: string;
  allowPersonCodeEdit: boolean;
  gender: string;
  birthdate: string;
  ssn: string;
  personCode: number;
  clientId: number;
  generatedId: number;
  organizationName: string;
  effectiveDate: string | null;
  expirationDate: string | null;
  employeeNo: number;
  organizationNo: number;
  processStatus: number;
  address1: string;
  address2: string;
  city: string;
  zip: string;
  state: string;
  isCOBSecondary: boolean | null;
  isCobra: boolean;
  isRetiree: boolean;
  isEGWP: boolean;
  egwpEffectiveDate: string | null;
  clientDefinedData: string;
  medDSubsidyEffectiveDate: string | null;
  hicNumber: string | null;
  groupNo: string;
  cobEffectiveDate: string | null;
  isCOBMandatory: boolean;
}

type MembersDetailFormData = {
  firstName: string;
  middleInitial: string;
  lastName: string;
  Age: string;
  gender: string;
  birthdate: string;
  address1: string;
  address2: string;
  email: string;
  city: string;
  state: {
    label: string;
    value: string;
  };
  phone: number;
  zip: string;
  ssn: number;
  personCode: number;
  clientId: number;
  generatedId: number;
  cobra: boolean;
  retiree: boolean;
  egwp: boolean;
  egwpEffectiveDate: string | null;
  clientDefinedData: string;
  medDSubsidyEffectiveDate: string | null;
  hicNumber: string | null;
  isCOBSecondary: boolean;
  isCOBMandatory: boolean;
  cobSecondaryDate: string | null;
  cobPrimaryDate: string | null;
};

export function MemberDetail<Data extends MembersDetailData>({
  supportClientDefinedData,
  data,
  ssn,
  apiInstance,
}: MemberDetailDataProps<Data>) {
  const {
    methodApi,
    getApi,
    memberIdHistory,
    organizationMeddsubsidy,
    organizationDetail,
  } = apiInstance;
  const { canViewWithPerm } = useContext(PermissionRoleContext);

  const formInstance = useForm<MembersDetailFormData>({
    mode: 'onChange',
  });

  const {
    register,
    watch,
    setValue,
    reset,
    handleSubmit,
    control,
    formState: { isSubmitting, errors, isDirty },
  } = formInstance;
  const { toast } = createStandaloneToast();
  const params = useParams();
  const orgId = params?.['organizationId'];
  const empId = params?.['employeeId'];

  const [SSN, setSSN] = useState<number>();
  const [ssnViewClicked, setssnViewClicked] = useState(false);
  const ssnHandler = () => {
    setSSN(ssn);
    setssnViewClicked(true);
  };

  const options: StateManagedSelect[] = STATE_CODE_MAPPING?.map((state) => ({
    label: state.label,
    value: state.value,
  }));

  const getLabelByValue = (value: string) => {
    const option = options.find((option) => option.value === value);
    return option ? option.label : '';
  };

  const label = getLabelByValue(data?.state);
  const defaultOption = useMemo(
    () => ({
      label: label,
      value: data?.state,
    }),
    [label, data?.state]
  );

  useEffect(() => {
    if (data) {
      Object?.keys(data).forEach((key: any) => {
        setValue(key, (data as Record<string, any>)[key]);
      });
    }
    if (data?.isCOBSecondary) {
      setValue('cobSecondaryDate', data?.cobEffectiveDate);
    } else {
      setValue('cobPrimaryDate', data?.cobEffectiveDate);
    }

    setValue('state', defaultOption);
    setValue('cobra', data?.isCobra);
    setValue('retiree', data?.isRetiree);
    setValue('egwp', data?.isEGWP);
  }, [data, setValue, defaultOption]);

  useEffect(() => {
    if (data?.groupNo)
      getApi('incentivePrograms', { orgId, groupNo: data?.groupNo });
  }, [getApi, orgId, data?.groupNo]);

  const age = calculateAge(data?.birthdate);

  const onSubmit = async (values: MembersDetailFormData) => {
    let newCOBSecondary: any = values?.isCOBSecondary;
    if (!values?.cobPrimaryDate && !values?.cobSecondaryDate) {
      newCOBSecondary = null;
    }

    const formattedGetData = { ...values, isCOBSecondary: newCOBSecondary };
    methodApi('updateEmployeeView', {
      method: 'PUT',
      restParams: { orgId: orgId, empId: empId },
      body: {
        ...formattedGetData,
        personCode: values?.personCode ? Number(values?.personCode) : null,
        birthdate: convertToISODate(values.birthdate),
        cobSecondaryDate: convertToISODate(values.cobSecondaryDate),
        cobPrimaryDate: convertToISODate(values.cobPrimaryDate),
        egwpEffectiveDate: convertToISODate(values.egwpEffectiveDate),
        medDSubsidyEffectiveDate: convertToISODate(
          values.medDSubsidyEffectiveDate
        ),
        state: values?.state?.value || data?.state,
        isCobra: values?.cobra,
        isEGWP: values.egwp,
        isRetiree: values.retiree,
        hicNumber: null,
        clientDefinedData: values.clientDefinedData,
      },
      onSuccess: () => {
        toast({
          title: '',
          description: 'Update Employee - Success',
          status: 'success',
          duration: 3000,
        });
        reset({}, { keepValues: true });
        getApi('employeeView', {
          orgId: orgId,
          empId: empId,
        });
      },

      onError: (err: any) => {
        toast({
          title: 'Update Employee',
          description:
            err.detail.split('Error: ')[1] || 'Update Employee - Failed',
          status: 'error',
          duration: 3000,
        });
      },
    });
  };

  const columnHelper = createColumnHelper<Record<string, any>>();

  const idHistoryColumns = [
    columnHelper.accessor('historicalId', {
      cell: (info) => info.getValue(),
    }),
    columnHelper.accessor('effectiveDate', {
      cell: (info) => (info.getValue() ? formatDate(info.getValue()) : ''),
      header: 'Effective Date',
    }),
    columnHelper.accessor('createdDate', {
      cell: (info) =>
        info.row.original.createdDate
          ? formatDate(info.row.original.createdDate)
          : '',
      header: 'Assignment Date',
    }),
  ];

  return (
    <AccordionWrapper title=" Members Detail">
      <FormProvider {...formInstance}>
        <form onSubmit={handleSubmit(onSubmit)}>
          <Flex gap={8} p={4}>
            <FormControl flex={4} isInvalid={!!errors.firstName}>
              <FormLabel margin={'2px'}>
                <Flex gap={2}>
                  <FormHelperText color="red">*</FormHelperText>
                  First Name
                </Flex>
              </FormLabel>

              <Input
                placeholder="First Name"
                border={
                  errors.firstName ? '1px solid red' : '1px solid lightgrey'
                }
                {...register('firstName', {
                  required: 'First Name is required',
                  pattern: {
                    value: /^[a-zA-Z _:;"',.-]+$/,
                    message:
                      'Name can only be an alphabetic character, -, ., or',
                  },
                })}
              />
              {errors.firstName && (
                <FormHelperText color="red">
                  <span>{errors.firstName.message}</span>
                </FormHelperText>
              )}
            </FormControl>
            <FormControl flex={4} isInvalid={!!errors.middleInitial}>
              <FormLabel margin={'2px'}>Middle Initial</FormLabel>

              <Input
                defaultValue={data?.middleInitial ?? ''}
                id="middleInitial"
                maxLength={1}
                border={
                  errors.middleInitial ? '1px solid red' : '1px solid lightgrey'
                }
                placeholder="Middle Initial"
                {...register('middleInitial', {
                  maxLength: 1,
                  pattern: {
                    value: /^[a-zA-Z]+$/,
                    message: 'Middle Initial must be an alphabetic character',
                  },
                })}
              />
              {errors.middleInitial && (
                <FormHelperText color="red">
                  <span>{errors.middleInitial.message}</span>
                </FormHelperText>
              )}
            </FormControl>
            <FormControl flex={4} isInvalid={!!errors.lastName}>
              <FormLabel margin={'2px'}>
                <Flex gap={2}>
                  <FormHelperText color="red">*</FormHelperText>
                  Last Name
                </Flex>
              </FormLabel>
              <Input
                placeholder="Last Name"
                border={
                  errors.lastName ? '1px solid red' : '1px solid lightgrey'
                }
                {...register('lastName', {
                  required: 'Last Name is required',
                  pattern: {
                    value: /^[a-zA-Z _:;"',.-]+$/,
                    message:
                      'Name can only be an alphabetic character, -, ., or',
                  },
                })}
              />
              {errors.lastName && (
                <FormHelperText color="red">
                  <span>{errors.lastName.message}</span>
                </FormHelperText>
              )}
            </FormControl>
          </Flex>

          <Flex gap={8} p={4}>
            <FormControl flex={4}>
              <FormLabel margin={'2px'}>
                <Flex gap={2}>
                  <FormHelperText color="red">*</FormHelperText>
                  Birth Date
                </Flex>
              </FormLabel>

              <Input
                required
                type="date"
                defaultValue={data?.birthdate}
                placeholder="Birth Date"
                {...register('birthdate')}
                max="9999-12-31"
              />
            </FormControl>
            <FormControl flex={4}>
              <FormLabel margin={'2px'}>Age</FormLabel>

              <Input
                placeholder="Age"
                value={age}
                {...register('Age')}
                disabled
              />
            </FormControl>
            <FormControl flex={4}>
              <FormLabel margin={'2px'}>
                <Flex gap={2}>
                  <FormHelperText color="red">*</FormHelperText>
                  Gender
                </Flex>
              </FormLabel>
              <Select id="id" {...register('gender')}>
                <option value="">Select gender</option>

                <option value="M">Male</option>

                <option value="F">Female</option>
                <option value="U">Unspecified</option>
              </Select>
            </FormControl>
          </Flex>
          <Divider
            my={4}
            width={'100%'}
            borderWidth="1px"
            borderColor={theme.colors.brand.lightgrey}
          />
          <h1 style={{ fontWeight: 'bold', margin: '5px' }}> Address</h1>
          <Flex gap={8} p={4}>
            <FormControl flex={4} isInvalid={!!errors.address1}>
              <FormLabel margin={'2px'}>
                <Flex gap={2}>
                  <FormHelperText color="red">*</FormHelperText>
                  Address 1
                </Flex>
              </FormLabel>

              <Input
                placeholder="Address 1"
                border={
                  errors.address1 ? '1px solid red' : '1px solid lightgrey'
                }
                {...register('address1', {
                  required: 'Address1 is required',
                })}
              />
              {errors.address1 && (
                <FormHelperText color="red">
                  <span>{errors.address1.message}</span>
                </FormHelperText>
              )}
            </FormControl>
            <FormControl flex={4}>
              <FormLabel margin={'2px'}>Address 2</FormLabel>

              <Input
                defaultValue={data?.address2}
                placeholder="Address 2"
                {...register('address2')}
              />
            </FormControl>
            <FormControl flex={4}>
              <FormLabel margin={'2px'}>Email</FormLabel>
              <Input placeholder="Email" {...register('email')} />
            </FormControl>
          </Flex>

          <Flex gap={8} p={4}>
            <FormControl flex={4} isInvalid={!!errors.city}>
              <FormLabel margin={'2px'}>
                <Flex gap={2}>
                  <FormHelperText color="red">*</FormHelperText>
                  City
                </Flex>
              </FormLabel>

              <Input
                placeholder="City"
                border={errors.city ? '1px solid red' : '1px solid lightgrey'}
                {...register('city', {
                  required: 'City is required',
                })}
              />
              {errors.city && (
                <FormHelperText color="red">
                  <span>{errors.city.message}</span>
                </FormHelperText>
              )}
            </FormControl>

            <FormControl flex={4} isInvalid={!!errors.state}>
              <FormLabel margin={'2px'}>
                <Flex gap={2}>
                  <FormHelperText color="red">*</FormHelperText>
                  State
                </Flex>
              </FormLabel>
              <Controller
                name="state"
                control={control}
                render={({ field }) => (
                  // @ts-ignore
                  <ReactSelect
                    {...field}
                    options={options}
                    styles={{
                      menu: (provided) => ({
                        ...provided,
                      }),
                    }}
                    isClearable={true}
                    maxMenuHeight={110}
                  />
                )}
              />
              {errors.state && (
                <FormHelperText color="red">
                  <span>{errors.state.message}</span>
                </FormHelperText>
              )}
            </FormControl>
            <FormControl flex={4}>
              <FormLabel margin={'2px'}>Phone</FormLabel>

              <Input placeholder="Phone" {...register('phone')} />
            </FormControl>
          </Flex>

          <Flex gap={8} p={4}>
            <FormControl width={'30%'} isInvalid={!!errors.zip}>
              <Flex gap={2}>
                <FormHelperText color="red">*</FormHelperText>
                <FormLabel margin={'2px'}>Zip</FormLabel>
                <Tooltip
                  label="Zip must include 5 digits (#####) or 5 digits-4 digits (#####-####)"
                  fontSize="md"
                  placement="top"
                  hasArrow
                >
                  <Flex marginTop={'5px'}>
                    <AiOutlineQuestionCircle />
                  </Flex>
                </Tooltip>
              </Flex>

              <Input
                id="zip"
                placeholder="Zip"
                type="text"
                maxLength={10}
                defaultValue={data?.zip}
                border={errors.zip ? '1px solid red' : '1px solid lightgrey'}
                {...register('zip', {
                  required: 'zip is required',
                  pattern: {
                    value: /^\d{5}(-\d{4})?$/,
                    message:
                      'Zip should be either 5 digits or 9 digits in the format "XXXXX" or "XXXXX-XXXX"',
                  },
                  onChange: (e: React.ChangeEvent<HTMLSelectElement>) => {
                    let value = e.target.value.replace(/\D/g, '');
                    if (value.length > 5) {
                      value = `${value.slice(0, 5)}${
                        value.slice(5, 9) ? '-' : ''
                      }${value.slice(5, 9)}`;
                    }
                    if (value.length > 10) {
                      value = value.slice(0, 10);
                    }
                    e.target.value = value;
                  },
                })}
              />

              {errors.zip && (
                <FormHelperText color="red">
                  <span>{errors.zip.message}</span>
                </FormHelperText>
              )}
            </FormControl>
          </Flex>

          <Divider
            my={4}
            width={'100%'}
            borderWidth="1px"
            borderColor={theme.colors.brand.lightgrey}
          />

          <AccordionPanel pb={4}>
            <h1 style={{ fontWeight: 'bold', margin: '5px' }}> Identity</h1>

            <Flex gap={8} p={4}>
              <FormControl flex={4}>
                <FormLabel margin={'2px'}>
                  <Flex gap={2}>
                    <FormHelperText color="red">*</FormHelperText>
                    SSN
                  </Flex>
                </FormLabel>
                <Flex>
                  <Input
                    placeholder={'XXX-XX-XXXX'}
                    backgroundColor={'#F5F5F5'}
                    {...register('ssn')}
                    value={ssnViewClicked ? SSN : ''}
                    disabled
                  />
                  <Button
                    backgroundColor={theme.colors.brand.white}
                    border={'1px solid #D9D9D9'}
                    color={'#335989'}
                    onClick={ssnHandler}
                  >
                    View
                  </Button>
                </Flex>
              </FormControl>
              <FormControl flex={4}>
                <Flex gap={2}>
                  <FormLabel margin={'2px'}>Person Code</FormLabel>
                  <Tooltip
                    label="Employee's Person Code generated by RxBenefits or provided by eligibility vendor"
                    fontSize="md"
                    placement="top"
                    hasArrow
                  >
                    <Flex marginTop={'5px'}>
                      <AiOutlineQuestionCircle />
                    </Flex>
                  </Tooltip>
                </Flex>

                <Input
                  defaultValue={data?.personCode}
                  {...register('personCode')}
                  isDisabled={!data?.allowPersonCodeEdit}
                />
              </FormControl>
            </Flex>

            <Flex gap={8} p={4}>
              <FormControl flex={4}>
                <Flex gap={2}>
                  <FormLabel margin={'2px'}>Client ID</FormLabel>
                  <Tooltip
                    label="Employee Client ID provided by eligibility vendor"
                    fontSize="md"
                    placement="top"
                    hasArrow
                  >
                    <Flex marginTop={'5px'}>
                      <AiOutlineQuestionCircle />
                    </Flex>
                  </Tooltip>
                </Flex>
                <Input
                  placeholder="Client Id"
                  defaultValue={data?.clientId ?? ''}
                  {...register('clientId')}
                  disabled
                />
              </FormControl>
              <FormControl flex={4}>
                <Flex gap={2}>
                  <FormLabel margin={'2px'}>Generated ID</FormLabel>
                  <Tooltip
                    label="Unique employee ID assigned by RxBenefits"
                    fontSize="md"
                    placement="top"
                    hasArrow
                  >
                    <Flex marginTop={'5px'}>
                      <AiOutlineQuestionCircle />
                    </Flex>
                  </Tooltip>
                </Flex>

                <Input
                  value={data?.generatedId}
                  disabled
                  {...register('generatedId')}
                />
              </FormControl>
            </Flex>

            {memberIdHistory?.length > 0 && (
              <AccordionWrapper title="Client ID History">
                <CustomReactTable
                  data={memberIdHistory}
                  columns={idHistoryColumns}
                />
              </AccordionWrapper>
            )}
          </AccordionPanel>

          <Divider
            my={4}
            width={'100%'}
            borderWidth="1px"
            borderColor={theme.colors.brand.lightgrey}
          />

          <Flex gap={8} p={4} flex={'row'} align={'center'}>
            <FormControl>
              {data?.isCobra !== undefined && (
                <Checkbox
                  size="md"
                  defaultChecked={data.isCobra}
                  {...register('cobra')}
                >
                  Cobra
                </Checkbox>
              )}
            </FormControl>

            <FormControl>
              {data?.isRetiree !== undefined && (
                <Checkbox
                  size="md"
                  defaultChecked={data.isRetiree}
                  {...register('retiree')}
                >
                  Retiree
                </Checkbox>
              )}
            </FormControl>

            <FormControl>
              {data?.isEGWP !== undefined && (
                <Checkbox
                  size="md"
                  defaultChecked={data.isEGWP}
                  {...register('egwp')}
                >
                  EGWP
                </Checkbox>
              )}
            </FormControl>
            {watch('egwp') && (
              <Flex gap={8} p={4}>
                <FormControl flex={4}>
                  <FormLabel margin={'2px'}>
                    <Flex gap={2}>
                      <FormHelperText color="red">*</FormHelperText>
                      EGWP Effective Date
                    </Flex>
                  </FormLabel>
                  <Input
                    type="date"
                    placeholder="EGWP Effective Date"
                    min={data?.groupEffectiveDate}
                    {...register('egwpEffectiveDate', {
                      required: 'EGWP Effective Date is required',
                    })}
                  />
                  {errors.egwpEffectiveDate && (
                    <FormHelperText color="red">
                      <span>
                        {(errors.egwpEffectiveDate as FieldError).message}
                      </span>
                    </FormHelperText>
                  )}
                </FormControl>
              </Flex>
            )}
          </Flex>

          <Divider
            my={4}
            width={'100%'}
            borderWidth="1px"
            borderColor={theme.colors.brand.lightgrey}
          />

          {organizationDetail?.useCOB && <COB data={data} />}

          {organizationMeddsubsidy?.length > 0 && (
            <MedDSubsidy showHIC={true} />
          )}

          <IncentivePrograms apiInstance={apiInstance} data={data} />

          {organizationDetail?.supportsClientDefinedData && (
            <FormControl flex={4} isInvalid={!!errors.clientDefinedData} p={4}>
              <Flex gap={2}>
                <FormLabel margin={'2px'}>
                  <Flex gap={2}>
                    <FormHelperText color="red">*</FormHelperText>
                    Client Defined Data
                  </Flex>
                </FormLabel>
                <Tooltip
                  label="Allows client defined data to be passed from the client to the PBM"
                  fontSize="md"
                  placement="top"
                  hasArrow
                >
                  <Flex marginTop={'7px'}>
                    <AiOutlineQuestionCircle />
                  </Flex>
                </Tooltip>
              </Flex>

              <Textarea
                placeholder="client defined data"
                border={
                  errors.firstName ? '1px solid red' : '1px solid lightgrey'
                }
                {...register('clientDefinedData')}
              />
              {errors.clientDefinedData && (
                <FormHelperText color="red">
                  <span>{errors.clientDefinedData.message}</span>
                </FormHelperText>
              )}
            </FormControl>
          )}
          {!!canViewWithPerm('editMembers') && (
            <Flex justify="flex-end" mt={4} mb={5} mr={5}>
              <Button
                isLoading={isSubmitting}
                type="submit"
                variant="solid"
                colorScheme="brand"
                bg="brand.700"
                isDisabled={!isDirty}
              >
                Save
              </Button>
            </Flex>
          )}
        </form>
      </FormProvider>
    </AccordionWrapper>
  );
}
