'use client';
import { Divider, Flex, Text } from '@chakra-ui/react';
import {
  CreateNewMember,
  CustomReactTable,
  MembersSearchForm,
} from '@next/admin/components';
import { theme } from '@next/admin/constants';
import { useApi } from '@next/shared/api';
import { formatDate } from '@next/shared/helpers';
import { useCustomText } from '@next/shared/hooks';
import { MemberFilters } from '@next/shared/types';
import { createColumnHelper } from '@tanstack/react-table';
import Link from 'next/link';
import {
  useParams,
  usePathname,
  useRouter,
  useSearchParams,
} from 'next/navigation';
import { useEffect, useMemo, useState } from 'react';

import Pagination from '../Global/Pagination';

export const MembersBase = ({ orgName = '' }) => {
  // Logic for pagination
  const router = useRouter();
  const searchParams = useSearchParams();
  const params = useParams();
  const page = searchParams?.get('page');
  const size = searchParams?.get('size');
  const pathname = usePathname();
  const currentPageNumber = useMemo(() => (page ? +page : 1), [page]);
  const rowsPerPage = useMemo(() => (size ? +size : 10), [size]);

  const createCustomText = useCustomText();
  const organizationId = params?.['organizationId'] || '';
  //   Fetching data
  const apiInstance = useApi(
    [
      'organizations',
      'organizationGroups',
      'organizationDetail',
      'medDSubsidyget',
      'benefitPackages',
    ],
    {
      orgId: +organizationId,
      orgOffset: 0,
      orgName: '',
    }
  );
  const {
    getApi,
    employeeListing,
    dependentListing,
    orgEmployeeListing,
    orgDependentListing,
    organizations,
    organizationGroups,
    organizationDetail,
    medDSubsidyget,
    benefitPackages,
  } = apiInstance;

  const [filters, setFilters] = useState<MemberFilters>({
    birthDate: '',
    effectiveDate: '',
    expirationDate: '',
    name: '',
    ssn: '',
    alternateId: '',
    status: '',
    orgID: '',
    memberType: 'employee',
  });
  const [organizationsFilterListData, setOrganizationsFilterListData] =
    useState<{ label: string; value: string }[]>([]);

  const KEY_TO_IGNORE_IN_FILTERS = 'memberType';

  const isAtleastOneFilterExist = Object.keys(filters).some(
    (key) => key !== KEY_TO_IGNORE_IN_FILTERS && (filters as any)[key] !== ''
  );

  //   Below is the code to setTotal data for pagination and data for TableView
  let totalCount;
  let memberListData;

  if (organizationId) {
    if (filters?.memberType === 'employee') {
      memberListData = orgEmployeeListing?.data;
      totalCount = orgEmployeeListing?.totalCount;
    } else {
      memberListData = orgDependentListing?.data;
      totalCount = orgDependentListing?.totalCount;
    }
  } else {
    if (filters?.memberType === 'employee') {
      memberListData = employeeListing?.data;
      totalCount = employeeListing?.totalCount;
    } else {
      memberListData = dependentListing?.data;
      totalCount = dependentListing?.totalCount;
    }
  }

  //   Below function return url to detail page of employee
  const getRedirectPath = (info: Record<string, any>) => {
    if (filters?.memberType === 'employee') {
      return `/clients-members/organizations/${
        organizationId || info.row.original.organizationNo
      }/employees/${info.row.original.employeeNo}`;
    } else {
      return `/clients-members/organizations/${
        organizationId || info.row.original.organizationNo
      }/employees/${info.row.original.employeeNo}/dependents/${
        info.row.original.dependentNo
      }`;
    }
  };

  //   Below is Orgnanization and Group list for filters

  useEffect(() => {
    if (organizations?.data) {
      const value = organizations?.data?.map((item: Record<string, any>) => {
        return {
          organizationNo: item?.organizationNo,
          organizationName: item?.name,
        };
      });

      // if scroll append data pahle ka logic
      if (organizations?.offset > 0) {
        setOrganizationsFilterListData((prev) => [...prev, ...value]);
      } else {
        setOrganizationsFilterListData([...value]);
      }
    }
  }, [organizations?.data, organizations?.offset]);

  const groupsFilterListData =
    organizationId && organizationGroups && !organizationGroups?.status
      ? organizationGroups?.map((item: Record<string, any>) => {
          return {
            groupNo: item?.benefitGroupNo,
            groupName: item?.name,
            processStatus: item?.processStatus,
          };
        })
      : [];

  const benefitPackagesListData =
    benefitPackages?.length && !benefitPackages?.processStatus
      ? benefitPackages.map((item: Record<string, any>) => {
          return {
            benefitPackageId: item?.benefitPackageId,
            benefitPackageName: item?.name,
            benefitPackageNo: item?.benefitPackageNo,
          };
        })
      : [];

  const getFilterValues = (filterValues: MemberFilters) => {
    setFilters({ ...filterValues });

    const params = new URLSearchParams(searchParams?.toString());
    router.push(`${pathname}?${params}`);
  };

  useEffect(() => {
    if (!organizationId && isAtleastOneFilterExist) {
      if (filters?.memberType === 'employee') {
        getApi('employeeListing', {
          ...filters,
          limit: rowsPerPage,
          offset: (currentPageNumber - 1) * rowsPerPage,
        });
      } else {
        getApi('dependentListing', {
          ...filters,
          limit: rowsPerPage,
          offset: (currentPageNumber - 1) * rowsPerPage,
        });
      }
    }
    if (organizationId) {
      if (filters?.memberType === 'employee') {
        getApi('orgEmployeeListing', {
          ...filters,
          orgId: +organizationId,
          limit: rowsPerPage,
          offset: (currentPageNumber - 1) * rowsPerPage,
        });
      } else {
        getApi('orgDependentListing', {
          ...filters,
          orgId: +organizationId,
          limit: rowsPerPage,
          offset: (currentPageNumber - 1) * rowsPerPage,
        });
      }
    }
  }, [
    filters,
    rowsPerPage,
    currentPageNumber,
    getApi,
    isAtleastOneFilterExist,
    organizationId,
  ]);

  const getCustomText = (data: Record<string, any>) => {
    const currentDate = new Date();
    const memberExpiration = data?.expirationDate || '';

    const isFutureTermination =
      data?.expirationDate && new Date(data.expirationDate) > currentDate;

    if (isFutureTermination) {
      return (
        <Flex gap={2} alignItems={'center'}>
          {createCustomText({
            processStatus: 1,
          })}
          {createCustomText({
            type: 'FutureTermination',
            text: `Future Termination (${formatDate(memberExpiration)})`,
          })}
        </Flex>
      );
    } else {
      return createCustomText({
        processStatus: data?.processStatus,
        expirationDate: data?.expirationDate,
      });
    }
  };

  const columnHelper = createColumnHelper<Record<string, any>>();
  const columns = [
    columnHelper.accessor('firstName', {
      cell: (info) => (
        <Link href={getRedirectPath(info)}>
          <span style={{ color: `${theme.colors.brand[700]}` }}>
            {info?.row?.original?.firstName}
          </span>
        </Link>
      ),
      header: 'First Name',
    }),
    columnHelper.accessor('lastName', {
      cell: (info) => (
        <Link href={getRedirectPath(info)}>
          <span style={{ color: `${theme.colors.brand[700]}` }}>
            {info?.row?.original?.lastName}
          </span>
        </Link>
      ),
      header: 'Last Name',
    }),
    columnHelper.accessor('organizationName', {
      cell: (info) => {
        // Check if organizationId is present in the data
        if (organizationId) {
          return info?.row?.original?.groupName; // Use groupName if organizationId is present
        } else {
          return info.getValue(); // Use organizationName if organizationId is not present
        }
      },
      header: organizationId ? 'Group' : 'Organization',
    }),
    columnHelper.accessor('generatedId', {
      cell: (info) => info.getValue(),
      header: 'Generated ID',
    }),
    columnHelper.accessor('clientId', {
      cell: (info) => info.getValue(),
      header: 'Client ID',
    }),
    columnHelper.accessor('birthdate', {
      cell: (info) => {
        const birthdate = info.getValue();
        return birthdate ? formatDate(birthdate) : '';
      },
      header: 'Birth Date',
    }),
    columnHelper.accessor('effectiveDate', {
      cell: (info) => {
        const effdate = info.getValue();
        return effdate ? formatDate(effdate) : '';
      },
      header: 'Effective Date',
    }),
    columnHelper.accessor('processStatus', {
      cell: (info) => getCustomText(info.row.original),
      header: 'Status',
    }),
  ];

  return (
    <Flex
      flexDirection={'column'}
      justifyContent={'center'}
      boxShadow="0 2px 4px rgba(0, 0, 0, 0.2)"
    >
      <CreateNewMember
        useCOB={organizationDetail?.useCOB}
        supportClientDefinedData={organizationDetail?.supportsClientDefinedData}
        orgName={orgName}
        groupData={groupsFilterListData}
        medDSubsidyArr={medDSubsidyget}
        benefitPackagesData={benefitPackagesListData}
      />
      <Divider />

      <MembersSearchForm
        getFilterValues={getFilterValues}
        filterList={
          organizationId ? groupsFilterListData : organizationsFilterListData
        }
        apiInstance={apiInstance}
      />
      {organizationId || isAtleastOneFilterExist ? (
        <CustomReactTable columns={columns} data={memberListData} />
      ) : (
        <Flex
          flexDirection={'column'}
          alignItems={'center'}
          gap={4}
          backgroundColor={theme.colors.brand.white}
          height="350px"
          width="100%"
          borderTop={`1px solid ${theme.colors.brand.lightgray}`}
          justifyContent={'center'}
        >
          <Text fontWeight={600} fontSize={18}>
            Use the filter options above to search for a member.
          </Text>
          <Text color={theme.colors.gray[700]}>
            You can use one or as many filter options as you would like.
          </Text>
        </Flex>
      )}

      <Pagination totalCount={totalCount} />
    </Flex>
  );
};
